1
00:00:00,000 --> 00:00:05,600
you can think it as a object which tells us about initial completion or failure of that particular

2
00:00:05,600 --> 00:00:15,040
process. So it's like a what we say that if the particular process is going to go into complete

3
00:00:15,040 --> 00:00:21,120
or it is going to fail. If it is going to complete then we will get a result promise and if it

4
00:00:21,120 --> 00:00:28,320
is going to fail then promise stands are rejected. To add on you can say it has mostly virtual

5
00:00:28,320 --> 00:00:36,560
DOM is present in react. So it is a copy of real DOM so that react can, react can apply

6
00:00:36,560 --> 00:00:44,960
updates to to the virtual DOM first by comparing the real DOM and virtual DOM. So that that makes the

7
00:00:44,960 --> 00:00:50,799
process efficient between synchronous and asynchronous code is that synchronous code happens

8
00:00:50,800 --> 00:00:59,200
step by step like it will first complete the first work in that event queue then only it will

9
00:00:59,200 --> 00:01:04,640
move forward to another second one then third one. So what it does because of this the main

10
00:01:04,640 --> 00:01:11,040
state gets blocked but various synchronous code happens with the help of we use callback and how

11
00:01:11,040 --> 00:01:20,000
also what because of that it moves to the side queue so that as it moves to the side queue because

