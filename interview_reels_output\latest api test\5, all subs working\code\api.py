#!/usr/bin/env python3
"""
FastAPI server for Enhanced Interview Reel Creator
Provides endpoints for reel creation and chapter detection
"""

import os
import sys
import json
import traceback
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

# Import the main reel creation functions
from main import (
    create_enhanced_interview_reels,
    get_topic_segments_from_llm,
    load_api_response,
    download_file,
    get_message_end_time,
    OUTPUT_DIR,
    VIDEO_FILENAME
)

app = FastAPI(
    title="Enhanced Interview Reel Creator API",
    description="API for creating interview reels and detecting chapters",
    version="1.0.0"
)

# Response models
class ReelCreationResponse(BaseModel):
    success: bool
    message: str
    reels_created: List[str] = []
    output_directory: str = ""

class Chapter(BaseModel):
    title: str
    start_time: float
    end_time: float
    duration: float
    description: str

class ChaptersResponse(BaseModel):
    success: bool
    message: str
    chapters: List[Chapter] = []
    total_duration: float = 0.0

# Background task status storage
task_status = {}

def get_chapters_from_llm(messages: List[Dict]) -> List[Dict]:
    """
    Get chapter segments from LLM based on conversation flow
    """
    try:
        import google.generativeai as genai
        from main import LLM_MODEL_NAME, genai
        
        conversation_text = "\n".join(
            f"[{msg.get('role', 'unknown')} at {msg.get('secondsFromStart'):.2f}s]: {msg.get('message', '')}"
            for msg in messages if msg.get('role') in ('user', 'bot') and msg.get('message')
        )

        prompt = f"""
        Analyze this interview conversation and identify natural chapter segments based on topic flow and conversation structure.
        Create chapters that represent distinct phases or topics of the interview.

        Conversation:
        {conversation_text}

        Please identify 4-8 meaningful chapters with clear topic boundaries. For each chapter, provide:
        1. A descriptive title (3-6 words)
        2. Start message index (which message starts this chapter)
        3. End message index (which message ends this chapter)
        4. Brief description of what's covered

        Return ONLY a JSON array in this exact format:
        [
            {{
                "title": "Introduction & Background",
                "start_index": 0,
                "end_index": 3,
                "description": "Initial introductions and background discussion"
            }},
            {{
                "title": "Technical Experience",
                "start_index": 4,
                "end_index": 8,
                "description": "Discussion of technical skills and experience"
            }}
        ]

        Ensure chapters don't overlap and cover the entire conversation logically.
        """

        model = genai.GenerativeModel(LLM_MODEL_NAME)
        response = model.generate_content(prompt)
        
        # Save LLM call log
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"chapters_llm_call_{timestamp}.txt"
        log_path = os.path.join(OUTPUT_DIR, "llm_logs", log_filename)
        os.makedirs(os.path.dirname(log_path), exist_ok=True)
        
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(f"Prompt:\n{prompt}\n\n")
            f.write(f"Response:\n{response.text}\n")
        
        print(f"LLM call logged to: {log_path}")

        # Parse JSON response
        json_text = response.text.strip()
        if json_text.startswith('```json'):
            json_text = json_text[7:]
        if json_text.endswith('```'):
            json_text = json_text[:-3]
        json_text = json_text.strip()

        chapters = json.loads(json_text)
        
        # Validate and process chapters
        processed_chapters = []
        for chapter in chapters:
            if (isinstance(chapter, dict) and
                'title' in chapter and
                'start_index' in chapter and
                'end_index' in chapter):
                
                start_idx = chapter['start_index']
                end_idx = chapter['end_index']
                
                if start_idx < len(messages) and end_idx < len(messages):
                    start_time = messages[start_idx].get('secondsFromStart', 0)
                    end_time = get_message_end_time(messages[end_idx]) or start_time + 30
                    
                    processed_chapters.append({
                        'title': chapter['title'],
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time,
                        'description': chapter.get('description', ''),
                        'start_index': start_idx,
                        'end_index': end_idx
                    })
        
        return processed_chapters

    except Exception as e:
        print(f"Error during AI chapter analysis: {e}")
        traceback.print_exc()
        return []

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Enhanced Interview Reel Creator API",
        "version": "1.0.0",
        "endpoints": {
            "/create_reels": "POST - Create interview reels from VAPI data",
            "/chapters": "GET - Get chapter breakdown of interview",
            "/status/{task_id}": "GET - Check background task status"
        }
    }

@app.post("/create_reels", response_model=ReelCreationResponse)
async def create_reels(background_tasks: BackgroundTasks):
    """
    Create interview reels automatically from the configured data source
    """
    try:
        # Load data from the configured source
        data = load_api_response()
        if not data:
            raise HTTPException(status_code=400, detail="Could not load interview data from configured source")

        artifact = data.get('artifact', data)
        messages = artifact.get('messages')
        video_url = artifact.get('videoRecordingUrl')

        if not video_url or not messages:
            raise HTTPException(status_code=400, detail="Invalid data: missing videoRecordingUrl or messages")

        # Download video if needed
        video_filepath = os.path.join(OUTPUT_DIR, VIDEO_FILENAME)
        if not os.path.exists(video_filepath):
            if not download_file(video_url, video_filepath):
                raise HTTPException(status_code=500, detail="Failed to download video file")

        # Create reels (this will run in the current thread for now)
        # In production, you might want to run this as a background task
        create_enhanced_interview_reels()

        # Check for created reel files
        reel_files = []
        subtitle_files = []
        if os.path.exists(OUTPUT_DIR):
            for file in os.listdir(OUTPUT_DIR):
                if file.endswith('.mp4') and (file.startswith('reel_') or file.startswith('introduction_reel_')):
                    reel_files.append(file)
                    base_name = os.path.splitext(file)[0]
                    srt_file = base_name + ".srt"
                    if os.path.exists(os.path.join(OUTPUT_DIR, srt_file)):
                        subtitle_files.append(srt_file)

        return ReelCreationResponse(
            success=True,
            message=f"Successfully created {len(reel_files)} reel(s) with subtitles",
            reels_created=reel_files,
            output_directory=OUTPUT_DIR
        )

    except Exception as e:
        print(f"Error creating reels: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error creating reels: {str(e)}")

@app.get("/chapters", response_model=ChaptersResponse)
async def get_chapters():
    """
    Get chapter breakdown of the interview with timestamps
    """
    try:
        # Load data from the configured source
        data = load_api_response()
        if not data:
            raise HTTPException(status_code=400, detail="Could not load interview data from configured source")

        artifact = data.get('artifact', data)
        messages = artifact.get('messages')

        if not messages:
            raise HTTPException(status_code=400, detail="Invalid data: missing messages")

        # Get chapters from LLM
        chapters_data = get_chapters_from_llm(messages)
        
        if not chapters_data:
            raise HTTPException(status_code=500, detail="Failed to generate chapters")

        # Convert to response format
        chapters = [
            Chapter(
                title=chapter['title'],
                start_time=chapter['start_time'],
                end_time=chapter['end_time'],
                duration=chapter['duration'],
                description=chapter['description']
            )
            for chapter in chapters_data
        ]

        total_duration = max(chapter.end_time for chapter in chapters) if chapters else 0.0

        return ChaptersResponse(
            success=True,
            message=f"Successfully identified {len(chapters)} chapters",
            chapters=chapters,
            total_duration=total_duration
        )

    except Exception as e:
        print(f"Error getting chapters: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error getting chapters: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "API is running"}

if __name__ == "__main__":
    print("Starting Enhanced Interview Reel Creator API...")
    print("Available endpoints:")
    print("  POST /create_reels - Create interview reels")
    print("  GET /chapters - Get chapter breakdown")
    print("  GET /health - Health check")
    print("  GET / - API information")

    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=True)
