# -*- coding: utf-8 -*-
"""
Enhanced Interview <PERSON>el Creator v3.1 - Real-ESRGAN Integration
"""

import os
import sys

try:
    import google.colab
    IN_COLAB = True
except ImportError:
    IN_COLAB = False

import json
import requests
import math
import warnings
import traceback
import time
import numpy as np
import cv2
from PIL import Image, ImageFont, ImageDraw, ImageFilter

try:
    from moviepy.editor import *
    from moviepy.video.io.VideoFileClip import <PERSON><PERSON>ileClip
    from moviepy.video.compositing.CompositeVideoClip import CompositeVideoClip
    from moviepy.video.compositing.concatenate import concatenate_videoclips
    from moviepy.video.VideoClip import TextClip, ImageClip, ColorClip
    from moviepy.audio.io.AudioFileClip import AudioFileClip
    import moviepy.audio.fx.all as afx
    import moviepy.video.fx.all as vfx
except ImportError as e:
    pass

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except (ImportError, ValueError) as e:
    MEDIAPIPE_AVAILABLE = False

try:
    from pydub import AudioSegment
    from pydub.silence import detect_nonsilent
except ImportError:
    pass

REALESRGAN_AVAILABLE = False
try:
    from realesrgan import RealESRGANer
    from basicsr.archs.rrdbnet_arch import RRDBNet
    from basicsr.utils.download_util import load_file_from_url
    REALESRGAN_AVAILABLE = True
except ImportError as e:
    pass

try:
    import google.generativeai as genai
except ImportError:
    pass

CUDA_AVAILABLE = False
NVENC_AVAILABLE = False

try:
    import torch
    if torch.cuda.is_available():
        CUDA_AVAILABLE = True
        device = "cuda"
        gpu_name = torch.cuda.get_device_name(0)

        if "A100" in gpu_name or "V100" in gpu_name or "T4" in gpu_name or "RTX" in gpu_name:
            NVENC_AVAILABLE = True
    else:
        device = "cpu"
except ImportError:
    device = "cpu"

REALESRGAN_MODELS = {
    'RealESRGAN_x4plus': {
        'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
        'scale': 4,
        'description': 'General images, 4x upscaling'
    },
    'RealESRGAN_x2plus': {
        'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth',
        'scale': 2,
        'description': 'General images, 2x upscaling'
    },
    'RealESRGAN_x4plus_anime_6B': {
        'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth',
        'scale': 4,
        'description': 'Anime/illustration images, 4x upscaling'
    }
}

REALESRGAN_MODEL_NAME = 'RealESRGAN_x2plus'
REALESRGAN_ENABLE = True
REALESRGAN_TILE_SIZE = 400 if CUDA_AVAILABLE else 200
REALESRGAN_TILE_PAD = 10
REALESRGAN_PRE_PAD = 0
REALESRGAN_HALF_PRECISION = True if CUDA_AVAILABLE else False
REALESRGAN_MODELS_DIR = "realesrgan_models"

realesrgan_upsampler = None

def setup_realesrgan():
    global realesrgan_upsampler

    if not REALESRGAN_AVAILABLE or not REALESRGAN_ENABLE:
        return False

    try:
        os.makedirs(REALESRGAN_MODELS_DIR, exist_ok=True)
        model_config = REALESRGAN_MODELS[REALESRGAN_MODEL_NAME]
        model_path = os.path.join(REALESRGAN_MODELS_DIR, f"{REALESRGAN_MODEL_NAME}.pth")

        if not os.path.exists(model_path):
            load_file_from_url(
                url=model_config['url'],
                model_dir=REALESRGAN_MODELS_DIR,
                progress=True,
                file_name=f"{REALESRGAN_MODEL_NAME}.pth"
            )

        if 'anime' in REALESRGAN_MODEL_NAME.lower():
            netscale = model_config['scale']
            model = RRDBNet(
                num_in_ch=3,
                num_out_ch=3,
                num_feat=64,
                num_block=6,
                num_grow_ch=32,
                scale=netscale
            )
        else:
            netscale = model_config['scale']
            model = RRDBNet(
                num_in_ch=3,
                num_out_ch=3,
                num_feat=64,
                num_block=23,
                num_grow_ch=32,
                scale=netscale
            )

        device_type = 'cuda' if CUDA_AVAILABLE else 'cpu'
        realesrgan_upsampler = RealESRGANer(
            scale=netscale,
            model_path=model_path,
            model=model,
            tile=REALESRGAN_TILE_SIZE,
            tile_pad=REALESRGAN_TILE_PAD,
            pre_pad=REALESRGAN_PRE_PAD,
            half=REALESRGAN_HALF_PRECISION,
            device=device_type
        )

        return True

    except Exception as e:
        return False

def enhance_frame_with_realesrgan(frame):
    global realesrgan_upsampler

    if not REALESRGAN_AVAILABLE or not REALESRGAN_ENABLE or realesrgan_upsampler is None:
        return frame

    try:
        if frame.dtype != np.uint8:
            frame = (frame * 255).astype(np.uint8)

        if len(frame.shape) == 3 and frame.shape[2] == 3:
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        else:
            frame_rgb = frame

        enhanced_frame, _ = realesrgan_upsampler.enhance(frame_rgb, outscale=1.0)

        if len(enhanced_frame.shape) == 3 and enhanced_frame.shape[2] == 3:
            enhanced_frame_bgr = cv2.cvtColor(enhanced_frame, cv2.COLOR_RGB2BGR)
        else:
            enhanced_frame_bgr = enhanced_frame

        return enhanced_frame_bgr

    except Exception as e:
        return frame

def apply_realesrgan_to_clip(clip, progress_callback=None):
    if not REALESRGAN_AVAILABLE or not REALESRGAN_ENABLE or realesrgan_upsampler is None:
        return clip

    def enhance_frame_func(get_frame, t):
        frame = get_frame(t)
        enhanced = enhance_frame_with_realesrgan(frame)

        if progress_callback:
            progress = t / clip.duration
            progress_callback(progress)

        return enhanced

    try:
        enhanced_clip = clip.fl(enhance_frame_func, apply_to=['mask'])
        return enhanced_clip

    except Exception as e:
        return clip

def enhance_frame_with_opencv(frame):
    try:
        import cv2

        if frame.dtype != np.uint8:
            frame = (frame * 255).astype(np.uint8)

        if len(frame.shape) == 3 and frame.shape[2] == 3:
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        else:
            frame_rgb = frame

        enhanced = frame_rgb.copy()

        kernel_sharpen = np.array([[-1,-1,-1],
                                   [-1, 9,-1],
                                   [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel_sharpen)
        enhanced = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)

        lab = cv2.cvtColor(enhanced, cv2.COLOR_RGB2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        l = clahe.apply(l)
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2RGB)

        enhanced = cv2.bilateralFilter(enhanced, 5, 50, 50)

        gamma = 1.1
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
        enhanced = cv2.LUT(enhanced, table)

        if len(enhanced.shape) == 3 and enhanced.shape[2] == 3:
            enhanced_bgr = cv2.cvtColor(enhanced, cv2.COLOR_RGB2BGR)
        else:
            enhanced_bgr = enhanced

        return enhanced_bgr

    except Exception as e:
        return frame

def apply_local_enhancement_to_clip(clip, progress_callback=None):
    def enhance_frame_func(get_frame, t):
        frame = get_frame(t)
        enhanced = enhance_frame_with_opencv(frame)

        if progress_callback:
            progress = t / clip.duration
            progress_callback(progress)

        return enhanced

    try:
        enhanced_clip = clip.fl(enhance_frame_func, apply_to=['mask'])
        return enhanced_clip

    except Exception as e:
        return clip

# GPU-Accelerated encoding parameters
def ensure_even_dimensions(clip):
    width = clip.w
    height = clip.h
    
    if width % 2 != 0 or height % 2 != 0:
        new_width = width if width % 2 == 0 else width - 1
        new_height = height if height % 2 == 0 else height - 1
        
        return clip.crop(x1=0, y1=0, x2=new_width, y2=new_height)
    
    return clip

def get_gpu_encoding_params():
    return {
        'codec': 'libx264',
        'audio_codec': 'aac',
        'preset': 'medium',
        'ffmpeg_params': [
            '-crf', '23',
            '-pix_fmt', 'yuv420p',
            '-profile:v', 'main',
            '-level', '4.0',
            '-movflags', '+faststart',
            '-metadata', 'title=Interview Reel',
            '-metadata', 'artist=AI Generated'
        ]
    }



warnings.filterwarnings("ignore", category=SyntaxWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

GOOGLE_API_KEY = os.environ.get("GOOGLE_API_KEY", "AIzaSyCxhaWlqcDsRgYMStXgRWXkDjl-fq386Wo")
LLM_MODEL_NAME = "gemini-1.5-pro"

if GOOGLE_API_KEY:
    try:
        genai.configure(api_key=GOOGLE_API_KEY)
    except Exception as e:
        pass

OUTPUT_DIR = "interview_reels_enhanced_v3.1"
ASSETS_DIR = "assets"
VIDEO_FILENAME = "source_interview.mp4"
TEMP_AUDIO_FILENAME = "temp_segment_audio.wav"
LOGO_FILENAME = os.path.join(ASSETS_DIR, "logo.png")
MUSIC_FILENAME = os.path.join(ASSETS_DIR, "background_music.mp3")

TEXT_COLOR = '#FFFFFF'
TEXT_FONTSIZE = 32
TEXT_FONT = 'Arial-Bold'
TEXT_BG_COLOR = 'rgba(0,0,0,0.3)'
TEXT_STRIP_WIDTH = 0.8
TEXT_POSITION = ('center', 'top')
TEXT_DURATION = 5

BORDER_WIDTH = 2
BORDER_COLOR = '#FFFFFF'

LOGO_SIZE_SMALL = 0.06
LOGO_SIZE_LARGE = 0.15
LOGO_POSITION_SMALL = ('right', 'bottom')
LOGO_POSITION_LARGE = ('center', 'center')
LOGO_FADE_DURATION = 2.0

CANDIDATE_NAME_COLOR = '#FFFFFF'
CANDIDATE_NAME_FONTSIZE = 24
CANDIDATE_NAME_POSITION = ('left', 'bottom')

NOSE_CENTER_OFFSET = 0.30
FACE_SMOOTHING = 0.05
CROP_RATIO = 9/16

BACKGROUND_MUSIC_VOLUME = 0.2
SILENCE_THRESHOLD_DBFS = -50
MIN_SILENCE_DURATION_MS = 500
NON_SILENT_PADDING_MS = 200
TRANSITION_DURATION = 0.3

def load_api_response():
    try:
        if 'API_RESPONSE_JSON' in globals():
            api_data = globals()['API_RESPONSE_JSON']
            return api_data

        json_file_path = "api_response.json"
        if os.path.exists(json_file_path):
            with open(json_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if content.strip().startswith('API_RESPONSE_JSON = {'):
                json_start = content.find('{')
                json_content = content[json_start:]
                api_data = json.loads(json_content)
            else:
                api_data = json.loads(content)

            return api_data

        return None

    except Exception as e:
        return None

def download_file(url, filename):
    if os.path.exists(filename):
        return True

    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()

        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        return True

    except Exception as e:
        return False

def get_message_end_time(message):
    start_sec = message.get('secondsFromStart')
    if start_sec is None:
        return None

    duration_ms = message.get('duration', 0)
    if duration_ms > 0:
        return start_sec + (duration_ms / 1000.0)

    if 'endTime' in message and 'time' in message:
        calculated_duration = message['endTime'] - message['time']
        if calculated_duration > 0:
            return start_sec + (calculated_duration / 1000.0)

    message_text = message.get('message', '')
    if message_text:
        word_count = len(message_text.split())
        estimated_duration = max(0.5, word_count / 2.5)
        return start_sec + estimated_duration

    return start_sec + 0.5

def get_candidate_name():
    try:
        return input("Enter candidate name: ").strip()
    except:
        return "Candidate"

def validate_timing_data_quality(messages):
    total_messages = 0
    complete_timing = 0
    
    for msg in messages:
        role = msg.get("role", "unknown")
        if role == "System":
            continue
            
        total_messages += 1
        
        has_start = msg.get('secondsFromStart') is not None
        has_duration = msg.get('duration', 0) > 0
        has_endtime = 'endTime' in msg and 'time' in msg
        
        if has_start and (has_duration or has_endtime):
            complete_timing += 1
    
    timing_quality = (complete_timing / total_messages * 100) if total_messages > 0 else 0
    return timing_quality



# =============================================================================
# ENHANCED FACE TRACKING - MediaPipe Implementation
# =============================================================================

def crop_to_face_mediapipe(clip, crop_ratio=CROP_RATIO):
    if not MEDIAPIPE_AVAILABLE:
        return apply_center_crop(clip, crop_ratio)

    mp_face_detection = mp.solutions.face_detection

    if CUDA_AVAILABLE:
        face_detection = mp_face_detection.FaceDetection(
            model_selection=1,
            min_detection_confidence=0.5,
        )
    else:
        face_detection = mp_face_detection.FaceDetection(
            model_selection=1,
            min_detection_confidence=0.6
        )

    clip_w, clip_h = clip.w, clip.h
    crop_w = int(clip_h * crop_ratio)
    crop_h = clip_h

    if crop_w > clip_w:
        crop_w = clip_w
        crop_h = int(clip_w / crop_ratio)

    smoothed_x = (clip_w - crop_w) // 2
    smoothed_y = (clip_h - crop_h) // 2

    frame_cache = {}

    def track_face_frame(get_frame, t):
        nonlocal smoothed_x, smoothed_y

        frame = get_frame(t)
        h, w = frame.shape[:2]

        cache_key = int(t * 10)

        frame_crop_w = int(h * crop_ratio)
        frame_crop_h = h

        if frame_crop_w > w:
            frame_crop_w = w
            frame_crop_h = int(w / crop_ratio)

        try:
            if cache_key not in frame_cache:
                rgb_frame = frame if len(frame.shape) == 3 else np.expand_dims(frame, axis=-1)

                if len(rgb_frame.shape) == 3 and rgb_frame.shape[2] == 3:
                    rgb_frame = rgb_frame[:, :, ::-1]

                results = face_detection.process(rgb_frame)
                frame_cache[cache_key] = results

                if len(frame_cache) > 100:
                    oldest_key = min(frame_cache.keys())
                    del frame_cache[oldest_key]
            else:
                results = frame_cache[cache_key]

            if results.detections:
                detection = max(results.detections, key=lambda d: d.score[0])
                bbox = detection.location_data.relative_bounding_box

                face_center_x = int((bbox.xmin + bbox.width/2) * w)
                face_center_y = int((bbox.ymin + bbox.height/2) * h)

                nose_offset_y = int(face_center_y - bbox.height * h * 0.2)

                target_x = max(0, min(face_center_x - frame_crop_w // 2, w - frame_crop_w))
                target_y = max(0, min(nose_offset_y - int(frame_crop_h * NOSE_CENTER_OFFSET), h - frame_crop_h))

                smoothing_factor = FACE_SMOOTHING * (2 if CUDA_AVAILABLE else 1)
                smoothed_x = int(smoothed_x * (1 - smoothing_factor) + target_x * smoothing_factor)
                smoothed_y = int(smoothed_y * (1 - smoothing_factor) + target_y * smoothing_factor)

        except Exception:
            pass

        smoothed_x = max(0, min(smoothed_x, w - frame_crop_w))
        smoothed_y = max(0, min(smoothed_y, h - frame_crop_h))

        if len(frame.shape) == 3:
            cropped = frame[smoothed_y:smoothed_y + frame_crop_h,
                          smoothed_x:smoothed_x + frame_crop_w, :]
        else:
            cropped = frame[smoothed_y:smoothed_y + frame_crop_h,
                          smoothed_x:smoothed_x + frame_crop_w]

        return cropped

    try:
        cropped_clip = clip.fl(track_face_frame, apply_to=['mask'])
        return cropped_clip
    except Exception as e:
        return apply_center_crop(clip, crop_ratio)

def apply_center_crop(clip, crop_ratio):
    target_w = int(clip.h * crop_ratio)
    target_h = clip.h

    if target_w > clip.w:
        target_w = clip.w
        target_h = int(clip.w / crop_ratio)

    return clip.fx(vfx.crop,
                   x_center=clip.w/2,
                   y_center=clip.h/2,
                   width=target_w,
                   height=target_h)

# =============================================================================
# ENHANCED UI COMPONENTS - v3.0 White/Black Theme
# =============================================================================

def create_white_border(video_width, video_height, duration):
    try:
        return None
    except Exception as e:
        return None

def create_modern_topic_card(
    text: str,
    video_width: int,
    video_height: int,
    duration: float,
    start_time: float = 0,
    font_path: str = 'Montserrat-Bold.ttf',
    text_color: tuple = (255, 255, 255),
    position: tuple = ('center', 'top'),
    strip_width: float = 0.8
):
    try:
        base_font_size = max(20, min(48, int(video_width / 25)))
        text_length = len(text)
        if text_length > 30:
            base_font_size = int(base_font_size * 0.7)
        elif text_length > 20:
            base_font_size = int(base_font_size * 0.8)

        try:
            font = ImageFont.truetype(font_path, base_font_size)
        except:
            font = ImageFont.load_default()

        text_upper = text.upper()

        text_bbox = font.getbbox(text_upper)
        text_w = text_bbox[2] - text_bbox[0] + 20
        text_h = text_bbox[3] - text_bbox[1] + 20
        img = Image.new('RGBA', (text_w, text_h), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        shadow_offset = (4, 4)
        shadow_color = (0, 0, 0, 128)
        draw.text((shadow_offset[0], shadow_offset[1]), text_upper, font=font, fill=shadow_color)
        
        try:
            img = img.filter(ImageFilter.GaussianBlur(2))
        except:
            pass

        draw.text((0, 0), text_upper, font=font, fill=text_color, stroke_width=1, stroke_fill=(255, 255, 255, 64))

        img_array = np.array(img)

        text_clip = ImageClip(img_array).set_duration(duration).set_start(start_time).set_position(position)
        text_clip = text_clip.fx(vfx.fadein, 0.5)
        
        def scale_func(t):
            if t < 0.5:
                return 1 + 0.1 * (t / 0.5)
            return 1.1
        text_clip = text_clip.resize(scale_func)

        strip_height = text_h + 10
        strip_clip = ImageClip(np.full((int(video_width * strip_width), strip_height, 3), (0, 0, 0), dtype='uint8'))
        strip_clip = strip_clip.set_opacity(0.3).set_duration(duration).set_start(start_time).set_position(position)

        composited = CompositeVideoClip([strip_clip, text_clip], size=(video_width, video_height))

        return composited

    except Exception as e:
        try:
            fallback_clip = TextClip(
                text.upper(),
                fontsize=base_font_size,
                color='white',
                font='Arial-Bold'
            )
            fallback_clip = fallback_clip.set_duration(duration).set_start(start_time).set_position(position)
            return fallback_clip
        except:
            return None

def create_topic_card(text, video_width, video_height, duration, start_time=0):
    return create_modern_topic_card(text, video_width, video_height, duration, start_time)

def create_candidate_name_overlay(candidate_name, video_width, video_height, duration):
    try:
        name_clip = TextClip(
            candidate_name,
            fontsize=CANDIDATE_NAME_FONTSIZE,
            color='white',
            font='Arial-Bold'
        )
        name_clip = name_clip.set_duration(duration)

        margin = int(video_width * 0.02)
        name_position = (margin, video_height - name_clip.h - margin)
        name_clip = name_clip.set_position(name_position)

        return name_clip

    except Exception as e:
        try:
            name_clip = TextClip(
                candidate_name,
                fontsize=24,
                color='white'
            )
            name_clip = name_clip.set_duration(duration)
            
            margin = int(video_width * 0.02)
            name_position = (margin, video_height - name_clip.h - margin)
            name_clip = name_clip.set_position(name_position)
            
            return name_clip
        except Exception as e2:
            return None

def create_small_logo(video_width, video_height, duration):
    if not os.path.exists(LOGO_FILENAME):
        return None

    try:
        logo_size = int(video_width * LOGO_SIZE_SMALL)
        margin = int(video_width * 0.02)

        logo_clip = ImageClip(LOGO_FILENAME)
        logo_clip = logo_clip.set_duration(duration)
        logo_clip = logo_clip.resize(width=logo_size)

        logo_x = video_width - logo_clip.w - margin
        logo_y = video_height - logo_clip.h - margin
        logo_clip = logo_clip.set_position((logo_x, logo_y))

        return logo_clip

    except Exception as e:
        return None

def create_end_logo_fade(video_width, video_height):
    if not os.path.exists(LOGO_FILENAME):
        return None

    try:
        logo_size = int(video_height * LOGO_SIZE_LARGE)

        logo_clip = ImageClip(LOGO_FILENAME)
        logo_clip = logo_clip.set_duration(LOGO_FADE_DURATION)
        logo_clip = logo_clip.resize(height=logo_size)
        logo_clip = logo_clip.set_position(LOGO_POSITION_LARGE)

        logo_clip = logo_clip.fx(vfx.fadein, 0.5).fx(vfx.fadeout, 0.5)

        return logo_clip

    except Exception as e:
        return None

# =============================================================================
# AUDIO PROCESSING - Silence Removal
# =============================================================================

def remove_silence_from_clip(video_clip, silence_thresh=SILENCE_THRESHOLD_DBFS,
                           min_silence_len=MIN_SILENCE_DURATION_MS,
                           padding=NON_SILENT_PADDING_MS):
    if video_clip.audio is None:
        return video_clip

    temp_audio_path = os.path.join(OUTPUT_DIR, TEMP_AUDIO_FILENAME)

    try:
        video_clip.audio.write_audiofile(temp_audio_path, logger=None, verbose=False)
        audio_segment = AudioSegment.from_file(temp_audio_path)

        non_silent_ranges = detect_nonsilent(
            audio_segment,
            min_silence_len=min_silence_len,
            silence_thresh=silence_thresh,
            seek_step=1
        )

        if not non_silent_ranges:
            return video_clip

        subclips = []
        padding_sec = padding / 1000.0

        for start_ms, end_ms in non_silent_ranges:
            start_sec = max(0, (start_ms / 1000.0) - padding_sec)
            end_sec = min(video_clip.duration, (end_ms / 1000.0) + padding_sec)

            if end_sec > start_sec + 0.1:
                subclips.append(video_clip.subclip(start_sec, end_sec))

        if subclips:
            processed_clip = concatenate_videoclips(subclips, method="compose")
            return processed_clip
        else:
            return video_clip

    except Exception as e:
        return video_clip
    finally:
        if os.path.exists(temp_audio_path):
            try:
                os.remove(temp_audio_path)
            except:
                pass

# =============================================================================
# ENHANCED LLM THEME DETECTION - v3.0 Multiple Topics Per Reel
# =============================================================================

def get_topic_segments_from_llm(messages):
    if not GOOGLE_API_KEY:
        return None

    conversation_text = ""
    for i, msg in enumerate(messages):
        role = msg.get("role", "unknown").capitalize()
        if role == "System":
            continue
        text = msg.get("message", "").strip()
        start_sec = msg.get("secondsFromStart", 0)
        end_sec = get_message_end_time(msg)
        
        duration_sec = 0.5
        if end_sec and start_sec is not None:
            duration_sec = max(0.5, end_sec - start_sec)
        
        if end_sec and start_sec is not None:
            conversation_text += f"[Start: {start_sec:.2f}s, End: {end_sec:.2f}s, Duration: {duration_sec:.2f}s] Message {i} ({role}): {text}\n"
        else:
            conversation_text += f"[{start_sec:.2f}s] Message {i} ({role}): {text}\n"

    prompt = f"""
ENHANCED INTERVIEW REEL CREATOR v3.0 - Multiple Topic Analysis

CRITICAL REQUIREMENT: One Reel = Multiple Topics with Contextual Cards
- Identify MULTIPLE topic moments within broader conversation themes
- Each topic card should appear at the exact moment when that specific topic is discussed
- Generate 3-8 topic cards per reel that capture different aspects of the discussion
- Focus on creating contextual overlays that enhance viewer understanding

CONVERSATION WITH ENHANCED TIMESTAMPS:
{conversation_text}

INSTRUCTIONS:
1. Identify 1-3 main REELS (broad conversation segments)
2. For each reel, identify 3-8 specific TOPIC MOMENTS
3. Each topic moment should have a short, impactful title (2-4 words)
4. Include precise timestamp when each topic is most relevant (use the Start time from messages)
5. Rate overall reel quality 1-5 based on content depth
6. Consider message duration when segmenting - longer messages may contain multiple topics

EXAMPLES OF TOPIC MOMENTS:
- At 45.00s: "Technical Background" (when candidate starts explaining experience)
- At 78.25s: "Key Challenge" (when specific problem is described)
- At 120.50s: "Solution Approach" (when methodology is explained)
- At 150.75s: "Results Impact" (when outcomes are discussed)

Return ONLY valid JSON:
[
  {{
    "reel_id": 1,
    "start_index": 2,
    "end_index": 8,
    "reel_title": "Data Science Experience",
    "quality_rating": 4,
    "topic_moments": [
      {{
        "timestamp": 45.00,
        "topic_text": "Technical Background",
        "message_index": 3
      }},
      {{
        "timestamp": 78.25,
        "topic_text": "Key Challenge",
        "message_index": 5
      }}
    ]
  }}
]
"""

    try:
        model = genai.GenerativeModel(LLM_MODEL_NAME)
        response = model.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(temperature=0.2)
        )

        llm_output = response.text.strip()

        if llm_output.startswith("```json"):
            llm_output = llm_output[7:]
        if llm_output.startswith("```"):
            llm_output = llm_output[3:]
        if llm_output.endswith("```"):
            llm_output = llm_output[:-3]
        llm_output = llm_output.strip()

        parsed_segments = json.loads(llm_output)

        if isinstance(parsed_segments, list) and all(
            isinstance(item, dict) and
            'start_index' in item and isinstance(item['start_index'], int) and
            'end_index' in item and isinstance(item['end_index'], int) and
            'reel_title' in item and isinstance(item['reel_title'], str) and
            'topic_moments' in item and isinstance(item['topic_moments'], list)
            for item in parsed_segments
        ):
            return parsed_segments
        else:
            return None

    except Exception as e:
        return None





def create_enhanced_interview_reels():
    realesrgan_ready = setup_realesrgan()
    
    data = load_api_response()
    if not data:
        return

    artifact = data.get('artifact', {})
    messages = artifact.get('messages') if artifact.get('messages') else data.get('messages')
    video_url = artifact.get('videoRecordingUrl')
    delay_seconds = artifact.get('videoRecordingStartDelaySeconds', 0)

    if not video_url or not messages:
        return

    candidate_name = get_candidate_name()
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    video_filepath = os.path.join(OUTPUT_DIR, VIDEO_FILENAME)
    if not download_file(video_url, video_filepath):
        return

    timing_quality = validate_timing_data_quality(messages)
    
    reel_segments = get_topic_segments_from_llm(messages)
    if not reel_segments:
        return

    try:
        main_clip = VideoFileClip(video_filepath, audio=True)
    except Exception as e:
        return

    processed_clips = []

    try:
        for reel_count, reel_info in enumerate(reel_segments, 1):
            start_idx = reel_info['start_index']
            end_idx = reel_info['end_index']
            reel_title = reel_info['reel_title']
            quality_rating = reel_info.get('quality_rating', 3)
            topic_moments = reel_info.get('topic_moments', [])

            user_segments = []
            segment_timestamps = []

            for i in range(start_idx, end_idx + 1):
                if i >= len(messages):
                    continue

                msg = messages[i]
                if msg.get('role') != 'user':
                    continue

                start_sec = msg.get('secondsFromStart')
                end_sec = get_message_end_time(msg)

                if start_sec is None or end_sec is None:
                    continue

                video_start = max(0, start_sec - delay_seconds)
                video_end = max(0, end_sec - delay_seconds)

                if video_end <= video_start + 0.1:
                    continue
                if video_start >= main_clip.duration:
                    continue

                video_end = min(video_end, main_clip.duration)

                try:
                    segment_clip = main_clip.subclip(video_start, video_end)
                    user_segments.append(segment_clip)
                    segment_timestamps.append({
                        'start': start_sec,
                        'end': end_sec,
                        'video_start': video_start,
                        'video_end': video_end
                    })

                except Exception as e:
                    continue

            if not user_segments:
                continue

            try:
                if len(user_segments) == 1:
                    combined_video = user_segments[0]
                else:
                    enhanced_segments = []
                    for i, segment in enumerate(user_segments):
                        if i > 0:
                            segment = segment.fx(vfx.fadein, TRANSITION_DURATION)
                        enhanced_segments.append(segment)

                    combined_video = concatenate_videoclips(enhanced_segments, method="compose")

            except Exception as e:
                continue

            try:
                face_tracked_video = crop_to_face_mediapipe(combined_video)
                face_tracked_video = ensure_even_dimensions(face_tracked_video)
            except Exception as e:
                face_tracked_video = combined_video
                face_tracked_video = ensure_even_dimensions(face_tracked_video)

            if realesrgan_ready and REALESRGAN_ENABLE:
                try:
                    def progress_callback(progress):
                        pass

                    enhanced_video = apply_realesrgan_to_clip(face_tracked_video, progress_callback)
                    face_tracked_video = enhanced_video
                except Exception as e:
                    pass

            video_width = face_tracked_video.w
            video_height = face_tracked_video.h
            video_duration = face_tracked_video.duration

            overlay_clips = [face_tracked_video]

            try:
                bordered_video = face_tracked_video.fx(vfx.margin, BORDER_WIDTH, color=(255,255,255))
                bordered_video = ensure_even_dimensions(bordered_video)
                overlay_clips = [bordered_video]
                video_width = bordered_video.w
                video_height = bordered_video.h
            except Exception as e:
                overlay_clips = [face_tracked_video]
                video_width = face_tracked_video.w
                video_height = face_tracked_video.h
                
            for topic_moment in topic_moments:
                try:
                    topic_text = topic_moment.get('topic_text', 'Topic')
                    topic_timestamp = topic_moment.get('timestamp', 0)
                    
                    adjusted_timestamp = max(0, topic_timestamp - delay_seconds)
                    
                    topic_card_clip = create_modern_topic_card(
                        topic_text, 
                        video_width, 
                        video_height, 
                        TEXT_DURATION, 
                        adjusted_timestamp
                    )
                    if topic_card_clip:
                        overlay_clips.append(topic_card_clip)

                except Exception as e:
                    pass

            try:
                candidate_name_clip = create_candidate_name_overlay(candidate_name, video_width, video_height, video_duration)
                if candidate_name_clip:
                    overlay_clips.append(candidate_name_clip)
            except Exception as e:
                pass

            try:
                small_logo_clip = create_small_logo(video_width, video_height, video_duration)
                if small_logo_clip:
                    overlay_clips.append(small_logo_clip)
            except Exception as e:
                pass

            try:
                end_logo_fade_clip = create_end_logo_fade(video_width, video_height)
                if end_logo_fade_clip:
                    end_start_time = max(0, video_duration - LOGO_FADE_DURATION)
                    end_logo_fade_clip = end_logo_fade_clip.set_start(end_start_time)
                    overlay_clips.append(end_logo_fade_clip)
            except Exception as e:
                pass

            try:
                final_reel = CompositeVideoClip(overlay_clips, size=(video_width, video_height))
                final_reel = ensure_even_dimensions(final_reel)
                
                try:
                    if hasattr(final_reel, 'audio') and final_reel.audio is not None:
                        final_reel = remove_silence_from_clip(final_reel)
                except Exception as e:
                    pass

                clean_title = "".join(c for c in reel_title if c.isalnum() or c in (' ', '-', '_')).strip()
                clean_title = clean_title.replace(' ', '_').lower()[:30]
                output_filename = f"{clean_title}_v3.1_realesrgan_enhanced.mp4"
                output_path = os.path.join(OUTPUT_DIR, output_filename)

                encoding_params = get_gpu_encoding_params()
                
                if final_reel.w % 2 != 0 or final_reel.h % 2 != 0:
                    final_reel = ensure_even_dimensions(final_reel)
                
                final_reel.write_videofile(
                    output_path,
                    codec=encoding_params['codec'],
                    audio_codec=encoding_params['audio_codec'],
                    preset=encoding_params.get('preset', 'medium'),
                    ffmpeg_params=encoding_params['ffmpeg_params'],
                    temp_audiofile=f'temp-audio-{reel_count}.m4a',
                    remove_temp=True,
                    logger='bar',
                    verbose=False,
                    threads=None if CUDA_AVAILABLE else 4
                )

                processed_clips.append(final_reel)

            except Exception as e:
                continue

    except Exception as e:
        pass

    finally:
        for clip in processed_clips:
            try:
                if hasattr(clip, 'close'):
                    clip.close()
            except:
                pass

        if 'main_clip' in locals() and hasattr(main_clip, 'close'):
            try:
                main_clip.close()
            except:
                pass

if __name__ == "__main__":
    create_enhanced_interview_reels()