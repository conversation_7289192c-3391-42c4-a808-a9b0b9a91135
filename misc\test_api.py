#!/usr/bin/env python3
"""
Test script for the Enhanced Interview Reel Creator API
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_root_endpoint():
    """Test the root endpoint"""
    print("\nTesting root endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_chapters_endpoint():
    """Test the chapters endpoint"""
    print("\nTesting chapters endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/chapters")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data['success']}")
            print(f"Message: {data['message']}")
            print(f"Total Duration: {data['total_duration']:.2f}s")
            print(f"Number of Chapters: {len(data['chapters'])}")
            
            for i, chapter in enumerate(data['chapters'], 1):
                print(f"  Chapter {i}: {chapter['title']}")
                print(f"    Time: {chapter['start_time']:.2f}s - {chapter['end_time']:.2f}s ({chapter['duration']:.2f}s)")
                print(f"    Description: {chapter['description']}")
        else:
            print(f"Error Response: {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_create_reels_endpoint():
    """Test the create reels endpoint"""
    print("\nTesting create reels endpoint...")
    print("Note: This will take several minutes to complete...")
    try:
        response = requests.post(f"{API_BASE_URL}/create_reels", timeout=300)  # 5 minute timeout
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data['success']}")
            print(f"Message: {data['message']}")
            print(f"Output Directory: {data['output_directory']}")
            print(f"Reels Created: {data['reels_created']}")
        else:
            print(f"Error Response: {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Run all API tests"""
    print("=== Enhanced Interview Reel Creator API Tests ===\n")
    
    tests = [
        ("Health Check", test_health_check),
        ("Root Endpoint", test_root_endpoint),
        ("Chapters Endpoint", test_chapters_endpoint),
        # ("Create Reels Endpoint", test_create_reels_endpoint),  # Commented out for quick testing
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        result = test_func()
        results[test_name] = result
        print(f"{'='*50}")
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY:")
    print(f"{'='*50}")
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed. Check the API server and try again.")

if __name__ == "__main__":
    main()
