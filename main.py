# -*- coding: utf-8 -*-
"""
Enhanced Interview <PERSON><PERSON> Creator v3.9 - Final Working Version
"""
import os
# --- Manual FFmpeg Configuration ---
# This forces MoviePy to use our manually downloaded FFmpeg
os.environ["IMAGEIO_FFMPEG_EXE"] = r"C:\ffmpeg\ffmpeg.exe"

import datetime  # For timestamps
from subs import generate_srt_from_mp4


import sys
import json
import requests
import math
import warnings
import traceback
import time
import numpy as np
import cv2
from PIL import Image, ImageFont, ImageDraw, ImageFilter

# --- Dependency Imports with Error Handling ---
try:
    from moviepy.editor import *
    import moviepy.video.fx.all as vfx
    MOVIEPY_AVAILABLE = True
except ImportError:
    print("FATAL: moviepy is not installed. Please run 'pip install moviepy==1.0.3'.")
    sys.exit()

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except (ImportError, ValueError):
    print("Warning: mediapipe not installed. Run 'pip install mediapipe'.")
    MEDIAPIPE_AVAILABLE = False

try:
    from pydub import AudioSegment
    from pydub.silence import detect_nonsilent
    PYDUB_AVAILABLE = True
except ImportError:
    print("Warning: pydub is not installed. Run 'pip install pydub'.")
    PYDUB_AVAILABLE = False

REALESRGAN_AVAILABLE = False
try:
    from realesrgan import RealESRGANer
    from basicsr.archs.rrdbnet_arch import RRDBNet
    from basicsr.utils.download_util import load_file_from_url
    REALESRGAN_AVAILABLE = True
    print("✅ Real-ESRGAN dependencies loaded successfully.")
except ImportError as e:
    print(f"Warning: Real-ESRGAN dependencies not found. Video enhancement will be skipped.")
    print(f"Import error: {e}")
    print("To enable video enhancement, run: pip install realesrgan basicsr")
    pass

try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    print("Warning: Google Generative AI SDK not found. Topic analysis will fail. Run 'pip install google-generativeai'.")
    GENAI_AVAILABLE = False

# --- GPU and Hardware Acceleration Check ---
CUDA_AVAILABLE = False
NVENC_AVAILABLE = False
try:
    import torch
    if torch.cuda.is_available():
        CUDA_AVAILABLE = True
        device = "cuda"
        gpu_name = torch.cuda.get_device_name(0)
        print(f"CUDA is available. Using GPU: {gpu_name}")
        if any(card in gpu_name for card in ["A100", "V100", "T4", "RTX", "Quadro", "Tesla"]):
            NVENC_AVAILABLE = True
            print("NVENC-compatible GPU detected. Will attempt hardware-accelerated encoding.")
    else:
        device = "cpu"
        print("CUDA not available. Running on CPU.")
except ImportError:
    device = "cpu"
    print("PyTorch not found. Running on CPU.")


# --- Global Configuration ---
REALESRGAN_MODELS = {
    'RealESRGAN_x4plus': {'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth', 'scale': 4},
    'RealESRGAN_x2plus': {'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth', 'scale': 2},
    'RealESRGAN_x4plus_anime_6B': {'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth', 'scale': 4}
}
# Real-ESRGAN Configuration - User configurable
REALESRGAN_MODEL_NAME = 'RealESRGAN_x2plus'  # Options: 'RealESRGAN_x2plus' (medium), 'RealESRGAN_x4plus' (high)
REALESRGAN_ENABLE = False  # Set to True to enable video enhancement (disabled for faster testing)
REALESRGAN_ENHANCEMENT_LEVEL = 'low'  # Options: 'low', 'medium', 'high'
REALESRGAN_TILE_SIZE = 400 if CUDA_AVAILABLE else 200
REALESRGAN_TILE_PAD = 10
REALESRGAN_HALF_PRECISION = True if CUDA_AVAILABLE else False
REALESRGAN_PRE_PAD = 0
REALESRGAN_MODELS_DIR = "realesrgan_models"

# Configure model based on enhancement level
if REALESRGAN_ENHANCEMENT_LEVEL == 'low':
    REALESRGAN_MODEL_NAME = 'RealESRGAN_x2plus'
    REALESRGAN_TILE_SIZE = 150 if CUDA_AVAILABLE else 150
elif REALESRGAN_ENHANCEMENT_LEVEL == 'medium':
    REALESRGAN_MODEL_NAME = 'RealESRGAN_x2plus'
    REALESRGAN_TILE_SIZE = 400 if CUDA_AVAILABLE else 200
elif REALESRGAN_ENHANCEMENT_LEVEL == 'high':
    REALESRGAN_MODEL_NAME = 'RealESRGAN_x4plus'
    REALESRGAN_TILE_SIZE = 500 if CUDA_AVAILABLE else 250

realesrgan_upsampler = None

# --- API KEY CONFIGURATION ---
API_KEY_STRING = "AIzaSyCxhaWlqcDsRgYMStXgRWXkDjl-fq386Wo" # <<< PASTE YOUR KEY HERE

if not GENAI_AVAILABLE or not API_KEY_STRING or API_KEY_STRING == "YOUR_API_KEY_HERE":
    print("🔴 ERROR: Script cannot run. Ensure 'google-generativeai' is installed and your API Key is set.")
    sys.exit()

# Configure the SDK once
genai.configure(api_key=API_KEY_STRING)

LLM_MODEL_NAME = "gemini-1.5-pro-latest"
OUTPUT_DIR = "interview_reels_output"
ASSETS_DIR = "assets"
VIDEO_FILENAME = "source_interview.mp4"
TEMP_AUDIO_FILENAME = "temp_segment_audio.wav"
LOGO_FILENAME = os.path.join(ASSETS_DIR, "logo.jpg")
MUSIC_FILENAME = os.path.join(ASSETS_DIR, "background_music.mp3")
FONT_FILENAME = os.path.join(ASSETS_DIR, "Roboto-Bold.ttf")
# --- Run labeling and feature toggles ---
RUN_LABEL = os.environ.get('RUN_LABEL', 'sched_outro_v2')

def _env_flag(name: str, default: bool) -> bool:
    val = os.environ.get(name)
    if val is None:
        return default
    return str(val).strip().lower() in ('1', 'true', 'yes', 'on')

FEATURE_SHOW_TOPIC_OVERLAYS = _env_flag('FEATURE_SHOW_TOPIC_OVERLAYS', True)
FEATURE_USE_STABLE_OUTRO = _env_flag('FEATURE_USE_STABLE_OUTRO', True)
FEATURE_INSERT_BLACK_PAD = _env_flag('FEATURE_INSERT_BLACK_PAD', False)
FEATURE_APPLY_FADEOUT = _env_flag('FEATURE_APPLY_FADEOUT', True)

# Outputs for this run go to a subfolder labeled with RUN_LABEL and timestamp
_RUN_TS = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
OUTPUT_RUN_DIR = os.path.join(OUTPUT_DIR, f"{RUN_LABEL}_{_RUN_TS}")
os.makedirs(OUTPUT_RUN_DIR, exist_ok=True)
print(f"Output run directory: {OUTPUT_RUN_DIR}")


# --- UI & Video Style Configuration ---
TEXT_COLOR = '#FFFFFF'
TEXT_FONTSIZE = 32
TEXT_FONT = os.path.join(ASSETS_DIR, "Roboto-Bold.ttf")
CANDIDATE_NAME_FONTSIZE = 24
TEXT_DURATION = 4
LOGO_FADE_DURATION = 2.0
CROP_RATIO = 9/16
FACE_SMOOTHING = 0.05
SILENCE_THRESHOLD_DBFS = -45
MIN_SILENCE_DURATION_MS = 400
NON_SILENT_PADDING_MS = 150
TRANSITION_DURATION = 0.3

warnings.filterwarnings("ignore", category=UserWarning)



# --- Core Functions ---

def setup_realesrgan():
    global realesrgan_upsampler
    if not REALESRGAN_AVAILABLE or not REALESRGAN_ENABLE:
        print("Real-ESRGAN is disabled or not available.")
        return False
    try:
        os.makedirs(REALESRGAN_MODELS_DIR, exist_ok=True)
        model_config = REALESRGAN_MODELS[REALESRGAN_MODEL_NAME]
        model_path = os.path.join(REALESRGAN_MODELS_DIR, f"{REALESRGAN_MODEL_NAME}.pth")

        if not os.path.exists(model_path):
            print(f"Downloading Real-ESRGAN model: {REALESRGAN_MODEL_NAME}...")
            load_file_from_url(
                url=model_config['url'],
                model_dir=REALESRGAN_MODELS_DIR,
                progress=True,
                file_name=f"{REALESRGAN_MODEL_NAME}.pth"
            )
        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=model_config['scale'])
        device_type = 'cuda' if CUDA_AVAILABLE else 'cpu'
        realesrgan_upsampler = RealESRGANer(
            scale=model_config['scale'],
            model_path=model_path,
            model=model,
            tile=REALESRGAN_TILE_SIZE,
            tile_pad=REALESRGAN_TILE_PAD,
            pre_pad=REALESRGAN_PRE_PAD,
            half=REALESRGAN_HALF_PRECISION,
            device=device_type
        )
        print(f"🚀 Real-ESRGAN model '{REALESRGAN_MODEL_NAME}' loaded successfully on {device_type.upper()}.")
        if CUDA_AVAILABLE:
            print(f"🎮 GPU acceleration enabled: {torch.cuda.get_device_name(0)}")
        return True
    except Exception as e:
        print(f"Error setting up Real-ESRGAN: {e}")
        traceback.print_exc()
        return False

def enhance_frame_with_realesrgan(frame):
    if realesrgan_upsampler is None: return frame
    try:
        enhanced_frame, _ = realesrgan_upsampler.enhance(frame)
        return enhanced_frame
    except Exception as e:
        return frame

def apply_realesrgan_to_clip(clip):
    if not REALESRGAN_AVAILABLE or not REALESRGAN_ENABLE or realesrgan_upsampler is None:
        print("Real-ESRGAN enhancement skipped (not available or disabled)")
        return clip
    print(f"🎨 Applying Real-ESRGAN enhancement ({REALESRGAN_ENHANCEMENT_LEVEL} level) to clip. This may take a while...")
    enhanced_clip = clip.fl_image(enhance_frame_with_realesrgan)
    print("✅ Real-ESRGAN enhancement completed")
    return enhanced_clip

def get_gpu_encoding_params():
    # This function is preserved but not used in the final step to ensure stability
    if NVENC_AVAILABLE:
        print("Using NVENC (GPU) for video encoding.")
        return {
            'codec': 'h264_nvenc', 'audio_codec': 'aac', 'preset': 'fast',
            'ffmpeg_params': ['-rc', 'vbr', '-cq', '24', '-pix_fmt', 'yuv420p', '-movflags', '+faststart']
        }
    else:
        print("Using libx264 (CPU) for video encoding.")
        return {
            'codec': 'libx264', 'audio_codec': 'aac', 'preset': 'medium',
            'ffmpeg_params': ['-crf', '23', '-pix_fmt', 'yuv420p', '-movflags', '+faststart']
        }

def load_api_response(filepath="api_response.json"):
    if not os.path.exists(filepath):
        print(f"Error: API response file not found at '{filepath}'")
        return None
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading file '{filepath}': {e}")
        return None

def download_file(url, filename):
    if os.path.exists(filename):
        print(f"File '{filename}' already exists. Skipping download.")
        return True
    try:
        print(f"Downloading from {url} to {filename}...")
        response = requests.get(url, stream=True, timeout=60)
        response.raise_for_status()
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print("Download complete.")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Error downloading file: {e}")
        return False

def get_message_end_time(message):
    try:
        start = float(message.get('secondsFromStart', 0))
    except (TypeError, ValueError):
        start = 0

    try:
        duration = float(message.get('duration', 0))
    except (TypeError, ValueError):
        duration = 0

    if duration > 0:
        return start + (duration / 1000)

    if 'endTime' in message and 'time' in message:
        try:
            end_time = float(message['endTime'])
            initial_time = float(message['time'])
            if end_time > initial_time:
                return start + (end_time - initial_time) / 1000
        except (TypeError, ValueError):
            pass

    text = message.get('message', '')
    if text:
        word_count = len(text.split())
        estimated = max(0.5, word_count / 2.5)
        return start + estimated

    return start + 0.5


def get_candidate_name():
    try:
        name = input("Enter candidate's name (or press Enter for 'Candidate'): ").strip()
        return name if name else "Candidate"
    except Exception:
        return "Candidate"

def apply_center_crop(clip, crop_ratio):
    w, h = clip.size
    target_h = h
    target_w = int(target_h * crop_ratio)
    if target_w > w:
        target_w = w
        target_h = int(w / crop_ratio)
    return clip.fx(vfx.crop, width=target_w, height=target_h, x_center=w/2, y_center=h/2)

def crop_to_face_mediapipe(clip, crop_ratio=CROP_RATIO):
    if not MEDIAPIPE_AVAILABLE:
        print("MediaPipe not available, using simple center crop.")
        return apply_center_crop(clip, crop_ratio)

    # --- CORRECTED LOGIC: Initialize detector WITHOUT a 'with' statement ---
    mp_face_detection = mp.solutions.face_detection
    face_detection = mp_face_detection.FaceDetection(model_selection=1, min_detection_confidence=0.3)

    clip_w, clip_h = clip.w, clip.h
    target_w = int(clip_h * crop_ratio)
    target_h = clip_h
    if target_w > clip_w:
        target_w = clip_w
        target_h = int(clip_w / crop_ratio)

    # Initialize with center position
    smoothed_x = (clip_w - target_w) / 2
    smoothed_y = (clip_h - target_h) / 2

    def track_face_frame(get_frame, t):
        nonlocal smoothed_x, smoothed_y
        frame = get_frame(t)

        # Ensure frame is in correct format for OpenCV
        if frame.dtype != np.uint8:
            frame = (frame * 255).astype(np.uint8)

        h, w, _ = frame.shape
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = face_detection.process(rgb_frame)

        # Default to center if no face detected
        target_x = (w - target_w) / 2
        target_y = (h - target_h) / 2

        if results.detections:
            best_detection = max(results.detections, key=lambda d: d.score[0])
            bbox = best_detection.location_data.relative_bounding_box

            # Calculate face center
            face_center_x = (bbox.xmin + bbox.width / 2) * w
            face_center_y = (bbox.ymin + bbox.height / 2) * h

            # Position crop to center the face, but with some headroom above
            target_x = face_center_x - (target_w / 2)
            target_y = face_center_y - (target_h * 0.4)  # Position face in upper 60% of frame

            # Ensure we don't go out of bounds
            target_x = max(0, min(target_x, w - target_w))
            target_y = max(0, min(target_y, h - target_h))

        # Apply smoothing for stable tracking
        smoothing_factor = FACE_SMOOTHING
        smoothed_x = (smoothed_x * (1 - smoothing_factor)) + (target_x * smoothing_factor)
        smoothed_y = (smoothed_y * (1 - smoothing_factor)) + (target_y * smoothing_factor)

        final_x = int(max(0, min(smoothed_x, w - target_w)))
        final_y = int(max(0, min(smoothed_y, h - target_h)))

        return frame[final_y : final_y + target_h, final_x : final_x + target_w]

    print("Applying MediaPipe face tracking and cropping...")
    return clip.fl(track_face_frame)

def create_white_border(video_width, video_height, duration):
    try:
        return None
    except Exception as e:
        return None

def create_modern_topic_card(text, video_width, video_height, duration, start_time):
    try:
        base_font_size = max(24, int(video_width / 20))
        if len(text) > 25:
            base_font_size = int(base_font_size * 0.8)
        font_path = FONT_FILENAME if os.path.exists(FONT_FILENAME) else 'Arial.ttf'
        try:
            font = ImageFont.truetype(font_path, base_font_size)
        except IOError:
            font = ImageFont.load_default()
        bbox = font.getbbox(text.upper())
        text_w, text_h = bbox[2] - bbox[0], bbox[3] - bbox[1]
        padding = 20
        img_w, img_h = text_w + padding*2, text_h + padding*2
        img = Image.new('RGBA', (img_w, img_h), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        draw.rounded_rectangle((0, 0, img_w, img_h), radius=15, fill=(0, 0, 0, 220))
        draw.text((padding, padding), text.upper(), font=font, fill=TEXT_COLOR)
        text_clip = ImageClip(np.array(img)) \
            .set_duration(duration) \
            .set_start(start_time) \
            .set_position(('center', 0.1), relative=True)
        return text_clip.fx(vfx.fadein, 0.3).fx(vfx.fadeout, 0.3)
    except Exception as e:
        print(f"Failed to create modern topic card, using fallback. Error: {e}")
        return TextClip(
            text.upper(), fontsize=TEXT_FONTSIZE, color='white',
            bg_color='rgba(0,0,0,0.5)', font=TEXT_FONT
        ).set_duration(duration).set_start(start_time).set_position(('center', 'top'))

def create_topic_card(text, video_width, video_height, duration, start_time=0):
    return create_modern_topic_card(text, video_width, video_height, duration, start_time)

def create_candidate_name_overlay(candidate_name, video_width, video_height, duration):
    try:
        name_clip = TextClip(
            candidate_name,
            fontsize=CANDIDATE_NAME_FONTSIZE,
            color='white',
            font='Arial-Bold'
        )
        name_clip = name_clip.set_duration(duration)

        margin = int(video_width * 0.02)
        name_position = (margin, video_height - name_clip.h - margin)
        name_clip = name_clip.set_position(name_position)

        return name_clip

    except Exception as e:
        try:
            name_clip = TextClip(
                candidate_name,
                fontsize=24,
                color='white'
            )
            name_clip = name_clip.set_duration(duration)

            margin = int(video_width * 0.02)
            name_position = (margin, video_height - name_clip.h - margin)
            name_clip = name_clip.set_position(name_position)

            return name_clip
        except Exception as e2:
            return None

def create_small_logo(video_width, video_height, duration):
    if not os.path.exists(LOGO_FILENAME):
        return None

    try:
        logo_size = int(video_width * 0.08)  # LOGO_SIZE_SMALL equivalent
        margin = int(video_width * 0.02)

        logo_clip = ImageClip(LOGO_FILENAME)
        logo_clip = logo_clip.set_duration(duration)
        logo_clip = logo_clip.resize(width=logo_size)

        logo_x = video_width - logo_clip.w - margin
        logo_y = video_height - logo_clip.h - margin
        logo_clip = logo_clip.set_position((logo_x, logo_y))

        return logo_clip

    except Exception as e:
        return None

def create_end_logo_fade(video_width, video_height):
    if not os.path.exists(LOGO_FILENAME):
        return None

    try:
        logo_size = int(video_height * 0.15)  # LOGO_SIZE_LARGE equivalent
        LOGO_FADE_DURATION = 2.0  # Define locally if not defined globally

        logo_clip = ImageClip(LOGO_FILENAME)
        logo_clip = logo_clip.set_duration(LOGO_FADE_DURATION)
        logo_clip = logo_clip.resize(height=logo_size)
        logo_clip = logo_clip.set_position('center')  # LOGO_POSITION_LARGE equivalent

        logo_clip = logo_clip.fx(vfx.fadein, 0.5).fx(vfx.fadeout, 0.5)

        return logo_clip

    except Exception as e:
        return None

def create_black_outro_with_logo(video_width, video_height, outro_duration=3.0):
    """Create a black outro screen with logo fade-in effect."""
    if not os.path.exists(LOGO_FILENAME):
        print("Logo file not found, creating simple black outro")
        return ColorClip(size=(video_width, video_height), color=(0, 0, 0)).set_duration(outro_duration)

    try:
        # Create black background
        black_bg = ColorClip(size=(video_width, video_height), color=(0, 0, 0)).set_duration(outro_duration)

        # Create logo
        logo_size = int(video_height * 0.2)  # Larger logo for outro
        logo_clip = ImageClip(LOGO_FILENAME)
        logo_clip = logo_clip.resize(height=logo_size)
        logo_clip = logo_clip.set_position('center')
        logo_clip = logo_clip.set_duration(outro_duration)

        # Add fade-in effect to logo (starts after 0.5s, fades in over 1s)
        logo_clip = logo_clip.set_start(0.5).fx(vfx.fadein, 1.0)

        # Composite black background with logo
        outro_clip = CompositeVideoClip([black_bg, logo_clip], size=(video_width, video_height))

        print(f"Created black outro with logo fade ({outro_duration}s)")
        return outro_clip

    except Exception as e:
        print(f"Error creating outro with logo: {e}")
        # Fallback to simple black screen
        return ColorClip(size=(video_width, video_height), color=(0, 0, 0)).set_duration(outro_duration)

def remove_silence_from_clip(video_clip, silence_thresh=SILENCE_THRESHOLD_DBFS,
                           min_silence_len=MIN_SILENCE_DURATION_MS,
                           padding=NON_SILENT_PADDING_MS):
    """Enhanced silence removal function with configurable parameters."""
    if video_clip.audio is None:
        return video_clip

    # Skip silence removal for concatenated clips to avoid CompositeAudioClip issues
    if hasattr(video_clip.audio, '__class__') and 'Composite' in video_clip.audio.__class__.__name__:
        print("Skipping silence removal for concatenated clip to avoid CompositeAudioClip issues")
        return video_clip

    # Fix for regular AudioClip fps issue
    if hasattr(video_clip.audio, 'fps') and video_clip.audio.fps is None:
        video_clip.audio.fps = 44100

    temp_audio_path = os.path.join(OUTPUT_DIR, TEMP_AUDIO_FILENAME)

    try:
        print("Analyzing audio to remove silent parts...")
        video_clip.audio.write_audiofile(temp_audio_path, logger=None, verbose=False)
        audio_segment = AudioSegment.from_file(temp_audio_path)

        non_silent_ranges = detect_nonsilent(
            audio_segment,
            min_silence_len=min_silence_len,
            silence_thresh=silence_thresh,
            seek_step=1
        )

        if not non_silent_ranges:
            print("No significant silences found.")
            return video_clip

        subclips = []
        padding_sec = padding / 1000.0




        # Ensure we capture the full audio by extending the last segment to the end
        for i, (start_ms, end_ms) in enumerate(non_silent_ranges):
            start_sec = max(0, (start_ms / 1000.0) - padding_sec)

            # For the last segment, extend to the end of the clip to avoid cutoff
            if i == len(non_silent_ranges) - 1:
                end_sec = video_clip.duration  # Use full duration for last segment
            else:
                end_sec = min(video_clip.duration, (end_ms / 1000.0) + padding_sec)

            if end_sec > start_sec + 0.1:  # Minimum clip duration
                subclips.append(video_clip.subclip(start_sec, end_sec))

        if subclips:
            processed_clip = concatenate_videoclips(subclips, method="compose")
            print(f"Silence removal complete. Original duration: {video_clip.duration:.2f}s, New duration: {processed_clip.duration:.2f}s")
            return processed_clip
        else:
            print("No valid clips after silence removal, returning original.")
            return video_clip

    except Exception as e:
        print(f"Could not perform silence removal. Error: {e}. Proceeding with original clip.")
        return video_clip
    finally:
        if os.path.exists(temp_audio_path):
            try:
                os.remove(temp_audio_path)
            except:
                pass

def log_llm_call(prompt, response_text, log_type="topic_llm_call"):
    """Logs the LLM prompt and response to a timestamped file."""
    try:
        log_dir = os.path.join(OUTPUT_DIR, "llm_logs")
        os.makedirs(log_dir, exist_ok=True)

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"{log_type}_{timestamp}.txt"
        log_path = os.path.join(log_dir, log_filename)

        with open(log_path, 'w', encoding='utf-8') as log_file:
            log_file.write(f"Timestamp: {datetime.datetime.now().isoformat()}\n\n")
            log_file.write("=== PROMPT ===\n")
            log_file.write(prompt + "\n\n")
            log_file.write("=== RESPONSE ===\n")
            log_file.write(response_text + "\n")

        print(f"LLM call logged to: {log_path}")
    except Exception as e:
        print(f"Warning: Failed to log LLM call: {e}")

def get_topic_segments_from_llm(messages):
    conversation_text = "\n".join(
    f"Index {i}: [{msg.get('role', 'unknown')} at {msg.get('secondsFromStart'):.2f}s]: {msg.get('message', '')}"
    for i, msg in enumerate(messages) if msg.get('role') in ('user', 'bot') and msg.get('message')
)
    prompt = f"""
    Analyze this interview transcript to create 1-3 short video reels. For each reel, pinpoint 2-4 specific "topic moments".

    Transcript:
    ---
    {conversation_text}
    ---

    Instructions:
    1. Identify 1-3 distinct `reel` segments ONLY if there are substantive, engaging technical discussions (e.g., avoid greetings or setup).
    2. For each reel, give a short, catchy `reel_title` that accurately matches the segment's content.
    3. Determine the `start_index` and `end_index` as the Index numbers from the transcript list.
    4. For each reel, identify 2-4 `topic_moments`, each with a `timestamp` and a short `topic_text`.
    5. Validate that the title directly relates to the topics in the segment.

    Return ONLY a valid JSON array of objects. Keep the same data type as input , i.e timestamps should not be in string type in output.
    """
    try:
        print("Contacting Google AI to analyze interview topics...")
        model = genai.GenerativeModel(LLM_MODEL_NAME)
        response = model.generate_content(prompt, generation_config={"temperature": 0.2})

        log_llm_call(prompt, response.text)
        json_text = response.text.strip().replace("```json", "").replace("```", "")
        parsed_segments = json.loads(json_text)

        validated_segments = []
        for seg in parsed_segments:
            if seg['end_index'] - seg['start_index'] >= 3:  # At least 3 messages for substance
                validated_segments.append(seg)
            else:
                print(f"Discarding short segment: {seg['reel_title']}")

        if isinstance(validated_segments, list) and all('reel_title' in item for item in validated_segments):
            print(f"AI analysis successful. Validated {len(validated_segments)} reel(s).")
            return validated_segments
        return None
    except Exception as e:
        print(f"Error during AI topic analysis: {e}")
        if "API key not valid" in str(e):
            print("Please check your API Key.")
        return None

def get_introduction_segment_from_llm(messages):
    """Separate LLM call to identify candidate introduction segments."""
    conversation_text = "\n".join(
        f"Index {i}: [{msg.get('role', 'unknown')} at {msg.get('secondsFromStart'):.2f}s]: {msg.get('message', '')}"
        for i, msg in enumerate(messages) if msg.get('role') in ('user', 'bot') and msg.get('message')
    )

    prompt = f"""
    Analyze this interview transcript to identify where the candidate introduces themselves. Look for segments where the candidate talks about their background, experience, or gives an overview of who they are.

    Transcript:
    ---
    {conversation_text}
    ---

    Instructions:
    1. Find the segment where the candidate introduces themselves (usually early in the interview).
    2. The introduction should be substantial (at least 2-3 messages from the candidate).
    3. Return a single introduction segment with:
       - `reel_title`: A catchy title like "Meet [Candidate Name]" or "Introduction"
       - `start_index` and `end_index`: Index numbers from the transcript
       - `topic_moments`: 1-2 key moments with timestamps and brief topic descriptions
    4. If no clear introduction is found, return null.

    Return ONLY a valid JSON object (not an array) or null. Keep the same data type as input , i.e timestamps should not be in string type in output.
    """

    try:
        print("Contacting Google AI to identify introduction segment...")
        model = genai.GenerativeModel(LLM_MODEL_NAME)
        response = model.generate_content(prompt, generation_config={"temperature": 0.2})

        log_llm_call(prompt, response.text, "introduction_llm_call")
        json_text = response.text.strip().replace("```json", "").replace("```", "")

        if json_text.lower().strip() == "null":
            print("No introduction segment found by AI.")
            return None

        parsed_segment = json.loads(json_text)

        # Validate the segment
        if (isinstance(parsed_segment, dict) and
            'start_index' in parsed_segment and
            'end_index' in parsed_segment and
            parsed_segment['end_index'] - parsed_segment['start_index'] >= 2):
            print("AI successfully identified introduction segment.")
            return parsed_segment
        else:
            print("Introduction segment validation failed.")
            return None

    except Exception as e:
        print(f"Error during AI introduction analysis: {e}")
        return None

def ensure_even_dimensions(clip):
    w, h = clip.w, clip.h
    if w % 2 != 0 or h % 2 != 0:
        return clip.crop(width=w - (w % 2), height=h - (h % 2))
    return clip

def extract_user_only_segments(main_clip, messages, start_idx, end_idx, delay_seconds):
    """Extract only user speaking segments from the video, preserving topic moment timing."""
    user_segments = []
    segment_info = []

    for idx in range(start_idx, end_idx + 1):
        if idx >= len(messages):
            break

        msg = messages[idx]
        if msg.get('role') != 'user':
            continue

        start_sec = float(msg.get('secondsFromStart', 0))
        end_sec = float(get_message_end_time(msg))

        if start_sec is None or end_sec is None:
            continue

        video_start = float(max(0, float(start_sec) - float(delay_seconds)))
        video_end = float(min(float(main_clip.duration), float(end_sec) - float(delay_seconds)))

        if float(video_end) <= float(video_start) + 0.1:  # Minimum segment duration
            continue

        try:
            segment_clip = main_clip.subclip(video_start, video_end)

            # Apply silence removal to individual segment before concatenation
            print(f"Applying silence removal to segment {len(user_segments)+1}...")
            segment_clip_processed = remove_silence_from_clip(segment_clip)

            user_segments.append(segment_clip_processed)
            segment_info.append({
                'original_start': start_sec,
                'original_end': end_sec,
                'video_start': video_start,
                'video_end': video_end,
                'duration': segment_clip_processed.duration  # Use processed duration
            })
        except Exception as e:
            print(f"Error extracting segment at {start_sec}s: {e}")
            continue

    if not user_segments:
        print("No user segments found in the specified range.")
        return None, []

    # Add smooth crossfade transitions between user segments
    try:
        if len(user_segments) == 1:
            combined_clip = user_segments[0]
        else:
            # Apply crossfade transitions between segments
            crossfade_duration = 0.3  # 300ms crossfade
            processed_segments = []

            for i, segment in enumerate(user_segments):
                if i == 0:
                    # First segment: fade in at start
                    processed_segments.append(segment.fx(vfx.fadein, crossfade_duration))
                elif i == len(user_segments) - 1:
                    # Last segment: fade out at end
                    processed_segments.append(segment.fx(vfx.fadeout, crossfade_duration))
                else:
                    # Middle segments: no fade (crossfade will handle transitions)
                    processed_segments.append(segment)

            # Concatenate with smooth transitions (sequential, not overlaid)
            combined_clip = concatenate_videoclips(processed_segments)

        # Fix audio fps issue for concatenated clips
        if combined_clip.audio is not None:
            if hasattr(combined_clip.audio, 'fps'):
                if combined_clip.audio.fps is None:
                    combined_clip.audio.fps = 44100
            else:
                # For CompositeAudioClip, ensure all underlying clips have fps
                if hasattr(combined_clip.audio, 'clips'):
                    for audio_clip in combined_clip.audio.clips:
                        if hasattr(audio_clip, 'fps') and audio_clip.fps is None:
                            audio_clip.fps = 44100

        print(f"Combined {len(user_segments)} user segments with smooth transitions into {combined_clip.duration:.2f}s video")
        return combined_clip, segment_info
    except Exception as e:
        print(f"Error combining user segments: {e}")
        return None, []

def adjust_topic_moments_for_user_segments(topic_moments, segment_info, original_start_time):
    """Adjust topic moment timings to match the new user-only video timeline."""
    adjusted_moments = []

    for topic in topic_moments:
        original_timestamp = topic.get('timestamp')
        if original_timestamp is None:
            continue

        # Find which segment this topic moment belongs to or should be moved to
        cumulative_duration = 0
        found_segment = False

        for i, segment in enumerate(segment_info):
            if (segment['original_start'] <= original_timestamp <= segment['original_end']):
                # Topic moment is within this user segment
                relative_pos = original_timestamp - segment['original_start']
                segment_duration = segment['original_end'] - segment['original_start']

                if segment_duration > 0:
                    relative_ratio = relative_pos / segment_duration
                    new_timestamp = cumulative_duration + (segment['duration'] * relative_ratio)

                    adjusted_topic = topic.copy()
                    adjusted_topic['timestamp'] = new_timestamp
                    adjusted_moments.append(adjusted_topic)
                    found_segment = True
                    break

            cumulative_duration += segment['duration']

        if not found_segment:
            # Topic moment was in a bot/silence segment, move it to the next user segment
            next_segment_index = None
            for i, segment in enumerate(segment_info):
                if segment['original_start'] > original_timestamp:
                    next_segment_index = i
                    break

            if next_segment_index is not None:
                # Place at the beginning of the next user segment
                cumulative_duration = sum(seg['duration'] for seg in segment_info[:next_segment_index])
                adjusted_topic = topic.copy()
                adjusted_topic['timestamp'] = cumulative_duration + 0.5  # Small offset from start
                adjusted_moments.append(adjusted_topic)
                print(f"Moved topic moment from {original_timestamp}s to start of next user segment at {adjusted_topic['timestamp']:.2f}s")
            else:
                # No next segment, place at the end of the last segment
                if segment_info:
                    total_duration = sum(seg['duration'] for seg in segment_info)
                    adjusted_topic = topic.copy()
                    adjusted_topic['timestamp'] = max(0, total_duration - 2.0)  # 2 seconds before end
                    adjusted_moments.append(adjusted_topic)
                    print(f"Moved topic moment from {original_timestamp}s to end of video at {adjusted_topic['timestamp']:.2f}s")


# --- Helper: ensure topic cards don't overlap ---
def schedule_topic_overlays(topic_moments, text_duration, total_duration, min_gap=0.1):
    """Return a list of (text, start, duration) with no overlaps.
    - Sorts by timestamp
    - If a card would overlap the previous one, it is shifted to start after the previous ends + min_gap
    - Clips durations at video end; drops cards that would be < 0.8s shown
    - De-duplicates cards that start within 0.3s with identical text
    """
    if not topic_moments:
        return []
    # Normalize and sort
    items = []
    for t in topic_moments:
        ts = t.get('timestamp')
        txt = t.get('topic_text') or t.get('text') or ''
        if ts is None or txt.strip() == '':
            continue
        items.append({'timestamp': float(ts), 'text': str(txt).strip()})
    items.sort(key=lambda x: x['timestamp'])

    scheduled = []
    last_end = -1e9
    for it in items:
        start = max(0.0, it['timestamp'])
        # shift to avoid overlap with previous
        if scheduled:
            start = max(start, last_end + min_gap)
        # clip duration inside total_duration
        max_dur = max(0.0, total_duration - start)
        dur = min(text_duration, max_dur)
        # skip if too short to be useful
        if dur < 0.2:
            continue
        # de-duplicate: if same text within 0.3s of previous start, skip
        if scheduled and scheduled[-1]['text'].lower() == it['text'].lower() and abs(scheduled[-1]['start'] - start) < 0.3:
            continue
        scheduled.append({'text': it['text'], 'start': start, 'duration': dur})
        last_end = start + dur
    return scheduled

# --- Helper: robustly append outro avoiding boundary artifacts ---
def append_outro_stable(final_reel, w, h, outro_duration=3.0, default_fps=30):
    try:
        # Resolve fps and enforce consistency
        target_fps = getattr(final_reel, 'fps', None) or default_fps
        safe_reel = final_reel.set_fps(target_fps)
        # Gentle fade out to avoid a hard cut exposing buffer artifacts
        safe_reel = safe_reel.fx(vfx.fadeout, 0.2)

        # Debug: print dimensions/fps to help diagnose artifacts
        try:
            print(f"append_outro_stable: final_reel size={safe_reel.w}x{safe_reel.h} fps={target_fps}")
        except Exception:
            pass

        outro_clip = create_black_outro_with_logo(w, h, outro_duration=outro_duration)
        outro_clip = outro_clip.set_fps(target_fps)
        try:
            print(f"append_outro_stable: outro_clip size={outro_clip.w}x{outro_clip.h} fps={target_fps}")
        except Exception:
            pass

        # Compose concatenation to unify size/masks/pixel formats
        stitched = concatenate_videoclips([safe_reel, outro_clip], method="compose")
        # Ensure even dimensions post-compose
        stitched = ensure_even_dimensions(stitched)
        return stitched
    except Exception as e:
        print(f"Warning: append_outro_stable failed, falling back. Error: {e}")
        # Fallback to previous behavior
        outro_clip = create_black_outro_with_logo(w, h, outro_duration=outro_duration)
        return ensure_even_dimensions(concatenate_videoclips([final_reel, outro_clip]))

def generate_reel_transcript(messages, start_idx, end_idx, reel_title, reel_count, output_dir=OUTPUT_DIR):
    """Generates and saves a transcript file for the reel based on message indices."""
    try:
        transcript_lines = []
        for idx in range(start_idx, end_idx + 1):
            if idx >= len(messages):
                break  # Safety check
            msg = messages[idx]
            role = msg.get('role', 'unknown')
            timestamp = msg.get('secondsFromStart', 0.0)
            message_text = msg.get('message', '')
            if message_text:  # Skip empty messages
                transcript_lines.append(f"[{role} at {timestamp:.2f}s]: {message_text}")

        if not transcript_lines:
            print(f"Warning: No transcript content found for reel #{reel_count}.")
            return

        clean_title = "".join(c for c in reel_title if c.isalnum() or c in (' ', '_')).strip().replace(' ', '_')
        transcript_path = os.path.join(output_dir or OUTPUT_RUN_DIR, f"reel_{reel_count}_{clean_title}_transcript.txt")

        with open(transcript_path, 'w', encoding='utf-8') as f:
            f.write(f"Reel Transcript: {reel_title}\n\n")
            f.write("\n".join(transcript_lines) + "\n")

        print(f"Transcript saved to: {transcript_path}")
    except Exception as e:
        print(f"Warning: Failed to generate transcript for reel #{reel_count}: {e}")

def create_enhanced_interview_reels():
    print("--- Starting Enhanced Interview Reel Creator ---")
    if not all([MOVIEPY_AVAILABLE, MEDIAPIPE_AVAILABLE, PYDUB_AVAILABLE, GENAI_AVAILABLE]):
        print("One or more required libraries are not installed. Please install them and try again.")
        return

    setup_realesrgan()
    data = load_api_response()
    if not data: return

    artifact = data.get('artifact', data)
    messages = artifact.get('messages')
    video_url = artifact.get('videoRecordingUrl')
    delay_seconds = float(artifact.get('videoRecordingStartDelaySeconds', 0))


    if not video_url or not messages:
        print("Critical data (videoRecordingUrl or messages) not found in JSON.")
        return

    candidate_name = get_candidate_name()
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(ASSETS_DIR, exist_ok=True)

    video_filepath = os.path.join(OUTPUT_DIR, VIDEO_FILENAME)
    if not download_file(video_url, video_filepath): return

    # First, try to create an introduction reel
    introduction_segment = get_introduction_segment_from_llm(messages)

    reel_segments = get_topic_segments_from_llm(messages)
    if not reel_segments:
        print("Could not generate reel segments from LLM. Exiting.")
        return
    print(f"AI identified {len(reel_segments)} potential reel(s).")

    main_clip = VideoFileClip(video_filepath)

    # Process introduction reel if found
    if introduction_segment:
        print(f"\n--- Processing Introduction Reel: '{introduction_segment['reel_title']}' ---")
        try:
            start_idx = introduction_segment['start_index']
            end_idx = introduction_segment['end_index']

            if start_idx >= 0 and start_idx < len(messages) and end_idx >= 0 and end_idx < len(messages) and start_idx <= end_idx:
                reel_title = introduction_segment['reel_title']
                topic_moments = introduction_segment.get('topic_moments', [])

                reel_start_time_sec = messages[start_idx].get('secondsFromStart')
                if reel_start_time_sec is not None:
                    video_start = max(0, reel_start_time_sec - delay_seconds)
                    video_end = min(main_clip.duration, get_message_end_time(messages[end_idx]) - delay_seconds)



                    if video_end > video_start:
                        generate_reel_transcript(messages, start_idx, end_idx, reel_title, 0)  # 0 for intro reel

                        # Extract only user segments for introduction reel
                        user_only_clip, segment_info = extract_user_only_segments(main_clip, messages, start_idx, end_idx, delay_seconds)
                        if user_only_clip is None:
                            print("No user segments found for introduction reel, skipping...")
                        else:
                            face_tracked_video = crop_to_face_mediapipe(user_only_clip)

                            # Apply Real-ESRGAN enhancement if enabled
                            if REALESRGAN_ENABLE and realesrgan_upsampler is not None:
                                print(f"Applying Real-ESRGAN enhancement (level: {REALESRGAN_ENHANCEMENT_LEVEL})...")
                                face_tracked_video = apply_realesrgan_to_clip(face_tracked_video)

                            w, h = face_tracked_video.size
                            duration = face_tracked_video.duration
                            overlays = [face_tracked_video]

                            # Adjust topic moments for user-only timeline
                            adjusted_topic_moments = adjust_topic_moments_for_user_segments(topic_moments, segment_info, reel_start_time_sec)

                            # Add topic overlays for introduction (no overlaps)
                            scheduled = schedule_topic_overlays(adjusted_topic_moments, TEXT_DURATION, duration)
                            print(f"[Topics Intro] Scheduled {len(scheduled)} topic cards")
                            for item in scheduled:
                                card = create_modern_topic_card(item['text'], w, h, item['duration'], item['start'])
                                if card:
                                    overlays.append(card)

                        if (name_overlay := create_candidate_name_overlay(candidate_name, w, h, duration)):
                            overlays.append(name_overlay)

                        final_reel = CompositeVideoClip(overlays, size=(w,h))
                        final_reel.audio = face_tracked_video.audio

                        # Silence removal already applied to individual segments
                        print("Silence removal completed on individual segments")

                        # [Removed outro for isolation]
                        # if FEATURE_INSERT_BLACK_PAD:
                        #     pad = ColorClip(size=(w,h), color=(0,0,0)).set_duration(0.05).set_fps(getattr(final_reel, 'fps', 30))
                        #     final_reel = concatenate_videoclips([final_reel, pad], method="compose")
                        # if FEATURE_USE_STABLE_OUTRO:
                        #     if FEATURE_APPLY_FADEOUT:
                        #         final_reel = final_reel.fx(vfx.fadeout, 0.2)
                        #     final_reel_output = append_outro_stable(final_reel, w, h, outro_duration=3.0)
                        # else:
                        #     outro_clip = create_black_outro_with_logo(w, h, outro_duration=3.0)
                        #     final_reel_output = ensure_even_dimensions(concatenate_videoclips([final_reel, outro_clip], method="compose"))
                        final_reel_output = ensure_even_dimensions(final_reel)

                        clean_title = "".join(c for c in reel_title if c.isalnum() or c in (' ', '_')).strip().replace(' ', '_')
                        output_path = os.path.join(OUTPUT_RUN_DIR, f"introduction_reel_{clean_title}.mp4")

                        print(f"Writing introduction reel to '{output_path}' using CPU encoder...")
                        final_reel_output.write_videofile(
                            output_path,
                            codec='libx264',
                            audio_codec='aac',
                            temp_audiofile='temp-audio-intro.m4a',
                            remove_temp=True,
                            threads=os.cpu_count() or 4,
                            logger=None,
                            preset='medium',
                            ffmpeg_params=['-pix_fmt', 'yuv420p', '-vf', 'pad=ceil(iw/2)*2:ceil(ih/2)*2']
                        )
                        try:
                            srt_output_path = os.path.splitext(output_path)[0] + ".srt"
                            generate_srt_from_mp4(output_path, srt_output_path, model_size='base')
                            print(f"Subtitle generated at: {srt_output_path}")
                        except Exception as e:
                            print(f"Failed to generate subtitles for {output_path}: {e}")
                        print(f"\n✅ Successfully saved introduction reel!")

        except Exception as e:
            print(f"Error processing introduction reel: {e}")
            traceback.print_exc()

    for reel_count, reel_info in enumerate(reel_segments, 1):
        try:
            start_idx = reel_info['start_index']
            end_idx = reel_info['end_index']
            # Add this block for safety
            if start_idx < 0 or start_idx >= len(messages) or end_idx < 0 or end_idx >= len(messages) or start_idx > end_idx:
                print(f"Skipping reel #{reel_count}: Invalid indices ({start_idx}-{end_idx}) for messages list of length {len(messages)}.")
                continue
            reel_title = reel_info['reel_title']
            topic_moments = reel_info.get('topic_moments', [])
            print(f"\n--- Processing Reel #{reel_count}: '{reel_title}' ---")

            reel_start_time_sec = messages[start_idx].get('secondsFromStart')
            if reel_start_time_sec is None: continue

            video_start = max(0, reel_start_time_sec - delay_seconds)
            video_end = min(main_clip.duration, get_message_end_time(messages[end_idx]) - delay_seconds)

            if video_end <= video_start: continue
            generate_reel_transcript(messages, start_idx, end_idx, reel_title, reel_count)

            # Extract only user segments for this reel
            user_only_clip, segment_info = extract_user_only_segments(main_clip, messages, start_idx, end_idx, delay_seconds)
            if user_only_clip is None:
                print(f"No user segments found for reel #{reel_count}, skipping...")
                continue

            face_tracked_video = crop_to_face_mediapipe(user_only_clip)

            # Apply Real-ESRGAN enhancement if enabled
            if REALESRGAN_ENABLE and realesrgan_upsampler is not None:
                print(f"Applying Real-ESRGAN enhancement (level: {REALESRGAN_ENHANCEMENT_LEVEL})...")
                face_tracked_video = apply_realesrgan_to_clip(face_tracked_video)

            w, h = face_tracked_video.size
            duration = face_tracked_video.duration
            overlays = [face_tracked_video]

            print("Adding overlays...")
            # Adjust topic moments for user-only timeline
            adjusted_topic_moments = adjust_topic_moments_for_user_segments(topic_moments, segment_info, reel_start_time_sec)

            scheduled = schedule_topic_overlays(adjusted_topic_moments, TEXT_DURATION, duration)
            print(f"[Topics Reel] Scheduled {len(scheduled)} topic cards")
            for item in scheduled:
                card = create_modern_topic_card(item['text'], w, h, item['duration'], item['start'])
                if card:
                    overlays.append(card)

            if (name_overlay := create_candidate_name_overlay(candidate_name, w, h, duration)):
                overlays.append(name_overlay)

            final_reel = CompositeVideoClip(overlays, size=(w,h))
            final_reel.audio = face_tracked_video.audio

            # Silence removal already applied to individual segments
            print("Silence removal completed on individual segments")

            # [Removed outro for isolation]
            # if FEATURE_INSERT_BLACK_PAD:
            #     pad = ColorClip(size=(w,h), color=(0,0,0)).set_duration(0.05).set_fps(getattr(final_reel, 'fps', 30))
            #     final_reel = concatenate_videoclips([final_reel, pad], method="compose")
            # if FEATURE_USE_STABLE_OUTRO:
            #     if FEATURE_APPLY_FADEOUT:
            #         final_reel = final_reel.fx(vfx.fadeout, 0.2)
            #     final_reel_output = append_outro_stable(final_reel, w, h, outro_duration=3.0)
            # else:
            #     outro_clip = create_black_outro_with_logo(w, h, outro_duration=3.0)
            #     final_reel_output = ensure_even_dimensions(concatenate_videoclips([final_reel, outro_clip], method="compose"))
            final_reel_output = ensure_even_dimensions(final_reel)

            clean_title = "".join(c for c in reel_title if c.isalnum() or c in (' ', '_')).strip().replace(' ', '_')
            output_path = os.path.join(OUTPUT_RUN_DIR, f"reel_{reel_count}_{clean_title}.mp4")

            # --- CORRECTED CODE: Forcing stable CPU encoding ---
            print(f"Writing final video to '{output_path}' using CPU encoder...")
            final_reel_output.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=f'temp-audio-{reel_count}.m4a',
                remove_temp=True,
                threads=os.cpu_count() or 4,
                logger=None,
                preset='medium',
                ffmpeg_params=['-pix_fmt', 'yuv420p', '-vf', 'pad=ceil(iw/2)*2:ceil(ih/2)*2']  # Auto-pad to even dimensions
            )
            try:
                srt_output_path = os.path.splitext(output_path)[0] + ".srt"
                generate_srt_from_mp4(output_path, srt_output_path, model_size='base')
                print(f"Subtitle generated at: {srt_output_path}")
            except Exception as e:
                print(f"Failed to generate subtitles for {output_path}: {e}")

            print(f"\n✅ Successfully saved reel #{reel_count}!")

        except Exception as e:
            print(f"!!! An unexpected error occurred while processing reel '{reel_title}' !!!")
            print(f"Error: {e}")
            traceback.print_exc()
            continue

    main_clip.close()
    print("\n--- All reels processed. ---")


if __name__ == "__main__":
    try:
        create_enhanced_interview_reels()
    except Exception as e:
        import traceback
        print("--- SCRIPT CRASHED: CAUGHT EXCEPTION ---")
        print(f"ERROR TYPE: {type(e).__name__}")
        print(f"ERROR MESSAGE: {e}")
        print("\n--- FULL TRACEBACK ---")
        traceback.print_exc()
        print("------------------------------------")