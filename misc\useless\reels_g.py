# -*- coding: utf-8 -*-
"""
Enhanced Interview <PERSON><PERSON> Creator v3.9 - Final Working Version
"""
import os
# --- Manual FFmpeg Configuration ---
# This forces MoviePy to use our manually downloaded FFmpeg
os.environ["IMAGEIO_FFMPEG_EXE"] = r"C:\ffmpeg\ffmpeg.exe"


import sys
import json
import requests
import math
import warnings
import traceback
import time
import numpy as np
import cv2
from PIL import Image, ImageFont, ImageDraw, ImageFilter

# --- Dependency Imports with Error Handling ---
try:
    from moviepy.editor import *
    import moviepy.video.fx.all as vfx
    MOVIEPY_AVAILABLE = True
except ImportError:
    print("FATAL: moviepy is not installed. Please run 'pip install moviepy==1.0.3'.")
    sys.exit()

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except (ImportError, ValueError):
    print("Warning: mediapipe not installed. Run 'pip install mediapipe'.")
    MEDIAPIPE_AVAILABLE = False

try:
    from pydub import AudioSegment
    from pydub.silence import detect_nonsilent
    PYDUB_AVAILABLE = True
except ImportError:
    print("Warning: pydub is not installed. Run 'pip install pydub'.")
    PYDUB_AVAILABLE = False

REALESRGAN_AVAILABLE = False
try:
    from realesrgan import RealESRGANer
    from basicsr.archs.rrdbnet_arch import RRDBNet
    from basicsr.utils.download_util import load_file_from_url
    REALESRGAN_AVAILABLE = True
except ImportError:
    print("Warning: Real-ESRGAN dependencies not found. Video enhancement will be skipped. Run 'pip install realesrgan'.")
    pass

try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    print("Warning: Google Generative AI SDK not found. Topic analysis will fail. Run 'pip install google-generativeai'.")
    GENAI_AVAILABLE = False

# --- GPU and Hardware Acceleration Check ---
CUDA_AVAILABLE = False
NVENC_AVAILABLE = False
try:
    import torch
    if torch.cuda.is_available():
        CUDA_AVAILABLE = True
        device = "cuda"
        gpu_name = torch.cuda.get_device_name(0)
        print(f"CUDA is available. Using GPU: {gpu_name}")
        if any(card in gpu_name for card in ["A100", "V100", "T4", "RTX", "Quadro", "Tesla"]):
            NVENC_AVAILABLE = True
            print("NVENC-compatible GPU detected. Will attempt hardware-accelerated encoding.")
    else:
        device = "cpu"
        print("CUDA not available. Running on CPU.")
except ImportError:
    device = "cpu"
    print("PyTorch not found. Running on CPU.")


# --- Global Configuration ---
REALESRGAN_MODELS = {
    'RealESRGAN_x4plus': {'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth', 'scale': 4},
    'RealESRGAN_x2plus': {'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.1/RealESRGAN_x2plus.pth', 'scale': 2},
    'RealESRGAN_x4plus_anime_6B': {'url': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth', 'scale': 4}
}
REALESRGAN_MODEL_NAME = 'RealESRGAN_x2plus'
REALESRGAN_ENABLE = True
REALESRGAN_TILE_SIZE = 400 if CUDA_AVAILABLE else 200
REALESRGAN_TILE_PAD = 10
REALESRGAN_PRE_PAD = 0
REALESRGAN_HALF_PRECISION = True if CUDA_AVAILABLE else False
REALESRGAN_MODELS_DIR = "realesrgan_models"

realesrgan_upsampler = None

# --- API KEY CONFIGURATION ---
API_KEY_STRING = "AIzaSyCxhaWlqcDsRgYMStXgRWXkDjl-fq386Wo" # <<< PASTE YOUR KEY HERE

if not GENAI_AVAILABLE or not API_KEY_STRING or API_KEY_STRING == "YOUR_API_KEY_HERE":
    print("🔴 ERROR: Script cannot run. Ensure 'google-generativeai' is installed and your API Key is set.")
    sys.exit()

# Configure the SDK once
genai.configure(api_key=API_KEY_STRING)

LLM_MODEL_NAME = "gemini-1.5-pro-latest"
OUTPUT_DIR = "interview_reels_output"
ASSETS_DIR = "assets"
VIDEO_FILENAME = "source_interview.mp4"
TEMP_AUDIO_FILENAME = "temp_segment_audio.wav"
LOGO_FILENAME = os.path.join(ASSETS_DIR, "logo.png")
MUSIC_FILENAME = os.path.join(ASSETS_DIR, "background_music.mp3")
FONT_FILENAME = os.path.join(ASSETS_DIR, "Roboto-Bold.ttf")

# --- UI & Video Style Configuration ---
TEXT_COLOR = '#FFFFFF'
TEXT_FONTSIZE = 32
TEXT_FONT = os.path.join(ASSETS_DIR, "Roboto-Bold.ttf")
CANDIDATE_NAME_FONTSIZE = 24
TEXT_DURATION = 4
LOGO_FADE_DURATION = 2.0
CROP_RATIO = 9/16
FACE_SMOOTHING = 0.05
SILENCE_THRESHOLD_DBFS = -45
MIN_SILENCE_DURATION_MS = 400
TRANSITION_DURATION = 0.3

warnings.filterwarnings("ignore", category=UserWarning)

# --- Core Functions ---

def setup_realesrgan():
    global realesrgan_upsampler
    if not REALESRGAN_AVAILABLE or not REALESRGAN_ENABLE:
        print("Real-ESRGAN is disabled or not available.")
        return False
    try:
        os.makedirs(REALESRGAN_MODELS_DIR, exist_ok=True)
        model_config = REALESRGAN_MODELS[REALESRGAN_MODEL_NAME]
        model_path = os.path.join(REALESRGAN_MODELS_DIR, f"{REALESRGAN_MODEL_NAME}.pth")

        if not os.path.exists(model_path):
            print(f"Downloading Real-ESRGAN model: {REALESRGAN_MODEL_NAME}...")
            load_file_from_url(
                url=model_config['url'],
                model_dir=REALESRGAN_MODELS_DIR,
                progress=True,
                file_name=f"{REALESRGAN_MODEL_NAME}.pth"
            )
        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=model_config['scale'])
        device_type = 'cuda' if CUDA_AVAILABLE else 'cpu'
        realesrgan_upsampler = RealESRGANer(
            scale=model_config['scale'],
            model_path=model_path,
            model=model,
            tile=REALESRGAN_TILE_SIZE,
            tile_pad=REALESRGAN_TILE_PAD,
            pre_pad=REALESRGAN_PRE_PAD,
            half=REALESRGAN_HALF_PRECISION,
            device=device_type
        )
        print(f"Real-ESRGAN model '{REALESRGAN_MODEL_NAME}' loaded successfully on {device_type}.")
        return True
    except Exception as e:
        print(f"Error setting up Real-ESRGAN: {e}")
        traceback.print_exc()
        return False

def enhance_frame_with_realesrgan(frame):
    if realesrgan_upsampler is None: return frame
    try:
        enhanced_frame, _ = realesrgan_upsampler.enhance(frame)
        return enhanced_frame
    except Exception as e:
        return frame

def apply_realesrgan_to_clip(clip):
    if not REALESRGAN_AVAILABLE or not REALESRGAN_ENABLE or realesrgan_upsampler is None:
        return clip
    print("Applying Real-ESRGAN enhancement to clip. This may take a while...")
    return clip.fl_image(enhance_frame_with_realesrgan)

def get_gpu_encoding_params():
    # This function is preserved but not used in the final step to ensure stability
    if NVENC_AVAILABLE:
        print("Using NVENC (GPU) for video encoding.")
        return {
            'codec': 'h264_nvenc', 'audio_codec': 'aac', 'preset': 'fast',
            'ffmpeg_params': ['-rc', 'vbr', '-cq', '24', '-pix_fmt', 'yuv420p', '-movflags', '+faststart']
        }
    else:
        print("Using libx264 (CPU) for video encoding.")
        return {
            'codec': 'libx264', 'audio_codec': 'aac', 'preset': 'medium',
            'ffmpeg_params': ['-crf', '23', '-pix_fmt', 'yuv420p', '-movflags', '+faststart']
        }

def load_api_response(filepath="api_response.json"):
    if not os.path.exists(filepath):
        print(f"Error: API response file not found at '{filepath}'")
        return None
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading file '{filepath}': {e}")
        return None

def download_file(url, filename):
    if os.path.exists(filename):
        print(f"File '{filename}' already exists. Skipping download.")
        return True
    try:
        print(f"Downloading from {url} to {filename}...")
        response = requests.get(url, stream=True, timeout=60)
        response.raise_for_status()
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print("Download complete.")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Error downloading file: {e}")
        return False

def get_message_end_time(message):
    start_sec = message.get('secondsFromStart')
    if start_sec is None: return None
    duration_ms = message.get('duration', 0)
    if duration_ms > 0:
        return start_sec + (duration_ms / 1000.0)
    if 'endTime' in message and 'time' in message:
        if isinstance(message['endTime'], (int, float)) and isinstance(message['time'], (int, float)):
            calculated_duration = message['endTime'] - message['time']
            if calculated_duration > 0:
                return start_sec + (calculated_duration / 1000.0)
    words_per_second = 2.5
    message_text = message.get('message', '')
    if message_text:
        word_count = len(message_text.split())
        estimated_duration = max(0.5, word_count / words_per_second)
        return start_sec + estimated_duration
    return start_sec + 0.5

def get_candidate_name():
    try:
        name = input("Enter candidate's name (or press Enter for 'Candidate'): ").strip()
        return name if name else "Candidate"
    except Exception:
        return "Candidate"

def apply_center_crop(clip, crop_ratio):
    w, h = clip.size
    target_h = h
    target_w = int(target_h * crop_ratio)
    if target_w > w:
        target_w = w
        target_h = int(w / crop_ratio)
    return clip.fx(vfx.crop, width=target_w, height=target_h, x_center=w/2, y_center=h/2)

def crop_to_face_mediapipe(clip, crop_ratio=CROP_RATIO):
    if not MEDIAPIPE_AVAILABLE:
        print("MediaPipe not available, using simple center crop.")
        return apply_center_crop(clip, crop_ratio)
    
    # --- CORRECTED LOGIC: Initialize detector WITHOUT a 'with' statement ---
    mp_face_detection = mp.solutions.face_detection
    face_detection = mp_face_detection.FaceDetection(model_selection=1, min_detection_confidence=0.5)

    clip_w, clip_h = clip.w, clip.h
    target_w = int(clip_h * crop_ratio)
    target_h = clip_h
    if target_w > clip_w:
        target_w = clip_w
        target_h = int(clip_w / crop_ratio)
    smoothed_x = (clip_w - target_w) / 2
    
    def track_face_frame(get_frame, t):
        nonlocal smoothed_x
        frame = get_frame(t)
        h, w, _ = frame.shape
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = face_detection.process(rgb_frame)
        target_x = (w - target_w) / 2
        if results.detections:
            best_detection = max(results.detections, key=lambda d: d.score[0])
            bbox = best_detection.location_data.relative_bounding_box
            face_center_x = (bbox.xmin + bbox.width / 2) * w
            target_x = face_center_x - (target_w / 2)
        smoothing_factor = FACE_SMOOTHING
        smoothed_x = (smoothed_x * (1 - smoothing_factor)) + (target_x * smoothing_factor)
        final_x = int(max(0, min(smoothed_x, w - target_w)))
        final_y = int((h - target_h) / 2)
        return frame[final_y : final_y + target_h, final_x : final_x + target_w]

    print("Applying MediaPipe face tracking and cropping...")
    return clip.fl(track_face_frame)

def create_modern_topic_card(text, video_width, video_height, duration, start_time):
    try:
        base_font_size = max(24, int(video_width / 20))
        if len(text) > 25:
            base_font_size = int(base_font_size * 0.8)
        font_path = FONT_FILENAME if os.path.exists(FONT_FILENAME) else 'Arial.ttf'
        try:
            font = ImageFont.truetype(font_path, base_font_size)
        except IOError:
            font = ImageFont.load_default()
        bbox = font.getbbox(text.upper())
        text_w, text_h = bbox[2] - bbox[0], bbox[3] - bbox[1]
        padding = 20
        img_w, img_h = text_w + padding*2, text_h + padding*2
        img = Image.new('RGBA', (img_w, img_h), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        draw.rounded_rectangle((0, 0, img_w, img_h), radius=15, fill=(0, 0, 0, 150))
        draw.text((padding, padding), text.upper(), font=font, fill=TEXT_COLOR)
        text_clip = ImageClip(np.array(img)) \
            .set_duration(duration) \
            .set_start(start_time) \
            .set_position(('center', 0.1), relative=True)
        return text_clip.fx(vfx.fadein, 0.5).fx(vfx.fadeout, 0.5)
    except Exception as e:
        print(f"Failed to create modern topic card, using fallback. Error: {e}")
        return TextClip(
            text.upper(), fontsize=TEXT_FONTSIZE, color='white',
            bg_color='rgba(0,0,0,0.5)', font=TEXT_FONT
        ).set_duration(duration).set_start(start_time).set_position(('center', 'top'))

def create_candidate_name_overlay(candidate_name, video_width, video_height, duration):
    try:
        margin = int(video_width * 0.04)
        name_clip = TextClip(
            candidate_name, fontsize=CANDIDATE_NAME_FONTSIZE, color='#FFFFFF',
            font=FONT_FILENAME if os.path.exists(FONT_FILENAME) else 'Arial.ttf'
        ).set_duration(duration).set_position((margin, video_height - 50))
        return name_clip
    except Exception as e:
        print(f"Error creating candidate name overlay: {e}")
        return None

def create_small_logo(video_width, video_height, duration):
    if not os.path.exists(LOGO_FILENAME):
        return None
    try:
        logo_size = int(video_width * 0.08)
        margin = int(video_width * 0.04)
        logo_img = ImageClip(LOGO_FILENAME)
        logo_aspect_ratio = logo_img.h / logo_img.w
        logo_clip = logo_img.set_duration(duration) \
            .resize(width=logo_size) \
            .set_position((video_width - logo_size - margin, video_height - int(logo_size * logo_aspect_ratio) - margin)) \
            .set_opacity(0.8)
        return logo_clip
    except Exception as e:
        print(f"Error creating small logo overlay: {e}")
        return None

def create_end_logo_fade(video_width, video_height):
    if not os.path.exists(LOGO_FILENAME): return None
    try:
        logo_size = int(video_height * 0.15)
        logo_clip = ImageClip(LOGO_FILENAME) \
            .set_duration(LOGO_FADE_DURATION) \
            .resize(height=logo_size) \
            .set_position('center')
        bg_clip = ColorClip(size=(video_width, video_height), color=(0, 0, 0)) \
            .set_duration(LOGO_FADE_DURATION).set_opacity(0)
        return CompositeVideoClip([bg_clip.fx(vfx.fadein, LOGO_FADE_DURATION), logo_clip.fx(vfx.fadein, 0.5)])
    except Exception as e:
        print(f"Error creating end logo fade: {e}")
        return None

def remove_silence_from_clip(video_clip):
    if not PYDUB_AVAILABLE or video_clip.audio is None:
        return video_clip
    
    if not hasattr(video_clip.audio, 'fps') or video_clip.audio.fps is None:
        print("Audio clip is missing FPS (sample rate). Setting to default 44100.")
        video_clip.audio.fps = 44100

    temp_audio_path = os.path.join(OUTPUT_DIR, TEMP_AUDIO_FILENAME)
    try:
        print("Analyzing audio to remove silent parts...")
        video_clip.audio.write_audiofile(temp_audio_path, codec='pcm_s16le', logger=None)
        audio_segment = AudioSegment.from_file(temp_audio_path)
        non_silent_ranges = detect_nonsilent(
            audio_segment, min_silence_len=MIN_SILENCE_DURATION_MS, silence_thresh=SILENCE_THRESHOLD_DBFS
        )
        if not non_silent_ranges:
            print("No significant silences found.")
            return video_clip

        subclips = []
        padding_sec = 0.15
        for start_ms, end_ms in non_silent_ranges:
            start_sec = max(0, start_ms / 1000.0 - padding_sec)
            end_sec = min(video_clip.duration, end_ms / 1000.0 + padding_sec)
            if end_sec > start_sec:
                subclips.append(video_clip.subclip(start_sec, end_sec))

        if subclips:
            print(f"Silence removal complete. Original duration: {video_clip.duration:.2f}s, New duration: {sum(c.duration for c in subclips):.2f}s")
            return concatenate_videoclips(subclips, method="compose")
        else:
            return video_clip
    except Exception as e:
        print(f"Could not perform silence removal. Error: {e}. Proceeding with original clip.")
        return video_clip
    finally:
        if os.path.exists(temp_audio_path):
            try: os.remove(temp_audio_path)
            except OSError: pass

def get_topic_segments_from_llm(messages):
    conversation_text = "\n".join(
        f"[{msg.get('role', 'unknown')} at {msg.get('secondsFromStart'):.2f}s]: {msg.get('message', '')}"
        for msg in messages if msg.get('role') in ('user', 'bot') and msg.get('message')
    )
    prompt = f"""
    Analyze this interview transcript to create 1-3 short video reels. For each reel, pinpoint 2-4 specific "topic moments".
    Transcript:
    ---
    {conversation_text}
    ---
    Instructions:
    1. Identify 1-3 distinct `reel` segments.
    2. For each reel, give a short, catchy `reel_title`.
    3. Determine the `start_index` and `end_index` from the message list for the reel.
    4. For each reel, identify 2-4 `topic_moments`, each with a `timestamp` and a short `topic_text`.
    Return ONLY a valid JSON array of objects.
    """
    try:
        print("Contacting Google AI to analyze interview topics...")
        model = genai.GenerativeModel(LLM_MODEL_NAME)
        response = model.generate_content(prompt, generation_config={"temperature": 0.2})
        json_text = response.text.strip().replace("```json", "").replace("```", "")
        parsed_segments = json.loads(json_text)
        if isinstance(parsed_segments, list) and all('reel_title' in item for item in parsed_segments):
            print("AI analysis successful.")
            return parsed_segments
        return None
    except Exception as e:
        print(f"Error during AI topic analysis: {e}")
        if "API key not valid" in str(e):
            print("Please check your API Key.")
        return None

def ensure_even_dimensions(clip):
    w, h = clip.w, clip.h
    if w % 2 != 0 or h % 2 != 0:
        return clip.crop(width=w - (w % 2), height=h - (h % 2))
    return clip

def create_enhanced_interview_reels():
    print("--- Starting Enhanced Interview Reel Creator ---")
    if not all([MOVIEPY_AVAILABLE, MEDIAPIPE_AVAILABLE, PYDUB_AVAILABLE, GENAI_AVAILABLE]):
        print("One or more required libraries are not installed. Please install them and try again.")
        return

    setup_realesrgan()
    data = load_api_response()
    if not data: return

    artifact = data.get('artifact', data)
    messages = artifact.get('messages')
    video_url = artifact.get('videoRecordingUrl')
    delay_seconds = artifact.get('videoRecordingStartDelaySeconds', 0)

    if not video_url or not messages:
        print("Critical data (videoRecordingUrl or messages) not found in JSON.")
        return

    candidate_name = get_candidate_name()
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(ASSETS_DIR, exist_ok=True)

    video_filepath = os.path.join(OUTPUT_DIR, VIDEO_FILENAME)
    if not download_file(video_url, video_filepath): return

    reel_segments = get_topic_segments_from_llm(messages)
    if not reel_segments:
        print("Could not generate reel segments from LLM. Exiting.")
        return
    print(f"AI identified {len(reel_segments)} potential reel(s).")

    main_clip = VideoFileClip(video_filepath)

    for reel_count, reel_info in enumerate(reel_segments, 1):
        try:
            start_idx = reel_info['start_index']
            end_idx = reel_info['end_index']
            reel_title = reel_info['reel_title']
            topic_moments = reel_info.get('topic_moments', [])
            print(f"\n--- Processing Reel #{reel_count}: '{reel_title}' ---")
            
            reel_start_time_sec = messages[start_idx].get('secondsFromStart')
            if reel_start_time_sec is None: continue

            video_start = max(0, reel_start_time_sec - delay_seconds)
            video_end = min(main_clip.duration, get_message_end_time(messages[end_idx]) - delay_seconds)

            if video_end <= video_start: continue
            
            reel_clip = main_clip.subclip(video_start, video_end)
            face_tracked_video = crop_to_face_mediapipe(reel_clip)

            w, h = face_tracked_video.size
            duration = face_tracked_video.duration
            overlays = [face_tracked_video]

            print("Adding overlays...")
            for topic in topic_moments:
                ts = topic.get('timestamp')
                txt = topic.get('topic_text')
                relative_ts = ts - reel_start_time_sec
                if txt and ts is not None and 0 <= relative_ts < duration:
                    card = create_modern_topic_card(txt, w, h, TEXT_DURATION, relative_ts)
                    if card: overlays.append(card)

            if (name_overlay := create_candidate_name_overlay(candidate_name, w, h, duration)):
                overlays.append(name_overlay)

            final_reel = CompositeVideoClip(overlays, size=(w,h))
            final_reel.audio = face_tracked_video.audio
            print("--- FINAL STABILITY FIX: Bypassing silence removal ---")
            final_reel_paced = final_reel
            
            # This is left commented out for stability until the main process is confirmed working.
            # if (end_card := create_end_logo_fade(w, h)):
            #     end_card = end_card.set_start(final_reel_paced.duration)
            #     final_reel_paced = concatenate_videoclips([final_reel_paced, end_card.set_duration(LOGO_FADE_DURATION)], method="compose")

            final_reel_output = ensure_even_dimensions(final_reel_paced)

            clean_title = "".join(c for c in reel_title if c.isalnum() or c in (' ', '_')).strip().replace(' ', '_')
            output_path = os.path.join(OUTPUT_DIR, f"reel_{reel_count}_{clean_title}.mp4")
            
            # --- CORRECTED CODE: Forcing stable CPU encoding ---
            print(f"Writing final video to '{output_path}' using CPU encoder...")
            final_reel_output.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=f'temp-audio-{reel_count}.m4a',
                remove_temp=True,
                threads=os.cpu_count() or 4,
                logger='bar'
            )
            print(f"\n✅ Successfully saved reel #{reel_count}!")

        except Exception as e:
            print(f"!!! An unexpected error occurred while processing reel '{reel_title}' !!!")
            print(f"Error: {e}")
            traceback.print_exc()
            continue

    main_clip.close()
    print("\n--- All reels processed. ---")


if __name__ == "__main__":
    try:
        create_enhanced_interview_reels()
    except Exception as e:
        import traceback
        print("--- SCRIPT CRASHED: CAUGHT EXCEPTION ---")
        print(f"ERROR TYPE: {type(e).__name__}")
        print(f"ERROR MESSAGE: {e}")
        print("\n--- FULL TRACEBACK ---")
        traceback.print_exc()
        print("------------------------------------")