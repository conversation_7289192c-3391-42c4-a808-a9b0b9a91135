Reel Transcript: NLP in Healthcare: Audio to SOAP Notes

[bot at 27.41s]: You're welcome. To outline the process, we will begin with a discussion about your experience in data science, and the specific technologies you've worked with. Then I will present some case study questions to evaluate your problem solving skills. Finally, we will assess your understanding of data science principles and methodologies. Let's start with your experience. Could you please provide an overview of your background in data science, including the specific technologies and tools you have utilized?
[user at 58.51s]: Yeah. So I am majorly focused on audio and text part of the things. My experience range is from doctor patient taking doctor patient transcripts using audio converting them to text, and then using various large language models to condense that into SOAP notes.
[bot at 83.96s]: Thank you for sharing that. It sounds like you have substantial experience in natural language processing. Particularly in the healthcare domain. Could you elaborate on the specific large language models you have worked with? And any frameworks or libraries you utilized for audio transcription and text processing. Additionally,
[user at 102.52s]: Yeah. So yeah. So I have used various sources for audio transcription, maybe be Amazon Medical Transcribe or Azure Realtime SDK. As well as Deepgram. Multiple have been used. Created custom vocabulary and custom systems to build better ASR systems.
