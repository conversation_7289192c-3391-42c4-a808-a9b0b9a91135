# Configuration Guide - Enhanced Interview Reel Creator v4.0

This guide explains how to configure the new features for optimal performance.

## 🔧 Silence Removal Configuration

### Basic Settings
```python
SILENCE_THRESHOLD_DBFS = -45        # Silence detection threshold
MIN_SILENCE_DURATION_MS = 400       # Minimum silence to remove
NON_SILENT_PADDING_MS = 150         # Padding around speech
```

### Tuning Guidelines

#### For High-Quality Audio (Studio/Professional)
```python
SILENCE_THRESHOLD_DBFS = -50        # More sensitive
MIN_SILENCE_DURATION_MS = 300       # Remove shorter silences
NON_SILENT_PADDING_MS = 100         # Less padding needed
```

#### For Low-Quality Audio (Phone/Compressed)
```python
SILENCE_THRESHOLD_DBFS = -35        # Less sensitive
MIN_SILENCE_DURATION_MS = 600       # Only remove longer silences
NON_SILENT_PADDING_MS = 200         # More padding for safety
```

#### For Fast-Paced Conversations
```python
SILENCE_THRESHOLD_DBFS = -40        # Moderate sensitivity
MIN_SILENCE_DURATION_MS = 500       # Remove natural pauses
NON_SILENT_PADDING_MS = 100         # Tight editing
```

## 🎨 Real-ESRGAN Video Enhancement Configuration

### Enhancement Levels

#### Low Quality (Fast Processing)
```python
REALESRGAN_ENHANCEMENT_LEVEL = 'low'
# Uses: RealESRGAN_x2plus, smaller tile size
# Best for: Quick processing, moderate improvement
# Processing time: ~2x original
```

#### Medium Quality (Balanced)
```python
REALESRGAN_ENHANCEMENT_LEVEL = 'medium'
# Uses: RealESRGAN_x2plus, standard tile size
# Best for: Good balance of quality and speed
# Processing time: ~3x original
```

#### High Quality (Best Results)
```python
REALESRGAN_ENHANCEMENT_LEVEL = 'high'
# Uses: RealESRGAN_x4plus, larger tile size
# Best for: Maximum quality improvement
# Processing time: ~5x original
```

### Hardware-Specific Settings

#### For GPU with 8GB+ VRAM
```python
REALESRGAN_ENHANCEMENT_LEVEL = 'high'
REALESRGAN_TILE_SIZE = 600
REALESRGAN_HALF_PRECISION = True
```

#### For GPU with 4-6GB VRAM
```python
REALESRGAN_ENHANCEMENT_LEVEL = 'medium'
REALESRGAN_TILE_SIZE = 400
REALESRGAN_HALF_PRECISION = True
```

#### For GPU with <4GB VRAM or CPU Only
```python
REALESRGAN_ENHANCEMENT_LEVEL = 'low'
REALESRGAN_TILE_SIZE = 200
REALESRGAN_HALF_PRECISION = False
# Or disable entirely:
REALESRGAN_ENABLE = False
```

## 🎯 Introduction Reel Configuration

### LLM Model Settings
```python
LLM_MODEL_NAME = "gemini-1.5-pro-latest"  # Best for analysis
# Alternative: "gemini-1.5-flash" for faster processing
```

### Introduction Detection Tuning

The introduction detection uses a separate LLM call with specific prompts. You can modify the prompt in the `get_introduction_segment_from_llm()` function for better results:

#### For Technical Interviews
- Looks for: background, experience, technical skills
- Keywords: "experience", "background", "worked with", "specialized in"

#### For General Interviews
- Looks for: personal introduction, career overview
- Keywords: "about myself", "my name is", "I am", "my background"

## 📊 Performance Optimization

### For Speed Priority
```python
# Disable video enhancement
REALESRGAN_ENABLE = False

# Faster silence removal
SILENCE_THRESHOLD_DBFS = -40
MIN_SILENCE_DURATION_MS = 500

# Use faster LLM model
LLM_MODEL_NAME = "gemini-1.5-flash"
```

### For Quality Priority
```python
# Enable high-quality enhancement
REALESRGAN_ENABLE = True
REALESRGAN_ENHANCEMENT_LEVEL = 'high'

# Precise silence removal
SILENCE_THRESHOLD_DBFS = -45
MIN_SILENCE_DURATION_MS = 300
NON_SILENT_PADDING_MS = 100

# Use best LLM model
LLM_MODEL_NAME = "gemini-1.5-pro-latest"
```

### For Balanced Performance
```python
# Medium enhancement
REALESRGAN_ENABLE = True
REALESRGAN_ENHANCEMENT_LEVEL = 'medium'

# Standard silence removal
SILENCE_THRESHOLD_DBFS = -45
MIN_SILENCE_DURATION_MS = 400
NON_SILENT_PADDING_MS = 150
```

## 🔍 Troubleshooting Common Issues

### Silence Removal Too Aggressive
**Problem**: Important speech is being cut
**Solution**: 
- Increase `NON_SILENT_PADDING_MS` to 200-300
- Decrease sensitivity: `SILENCE_THRESHOLD_DBFS = -35`

### Silence Removal Not Effective
**Problem**: Too many silent parts remain
**Solution**:
- Increase sensitivity: `SILENCE_THRESHOLD_DBFS = -50`
- Decrease `MIN_SILENCE_DURATION_MS` to 200-300

### Video Enhancement Too Slow
**Problem**: Processing takes too long
**Solution**:
- Use `REALESRGAN_ENHANCEMENT_LEVEL = 'low'`
- Reduce `REALESRGAN_TILE_SIZE`
- Or disable: `REALESRGAN_ENABLE = False`

### No Introduction Reel Generated
**Problem**: Introduction segment not detected
**Solution**:
- Check if interview has clear self-introduction
- Modify the LLM prompt to be less strict
- Manually verify transcript has introduction content

### GPU Memory Issues
**Problem**: CUDA out of memory errors
**Solution**:
- Reduce `REALESRGAN_TILE_SIZE`
- Enable `REALESRGAN_HALF_PRECISION = True`
- Use lower enhancement level

## 📝 Custom Configuration Template

Create a `config.py` file with your preferred settings:

```python
# Custom configuration for Enhanced Interview Reel Creator

# Silence Removal Settings
SILENCE_THRESHOLD_DBFS = -45
MIN_SILENCE_DURATION_MS = 400
NON_SILENT_PADDING_MS = 150

# Video Enhancement Settings
REALESRGAN_ENABLE = True
REALESRGAN_ENHANCEMENT_LEVEL = 'medium'  # 'low', 'medium', 'high'

# LLM Settings
LLM_MODEL_NAME = "gemini-1.5-pro-latest"

# Hardware Settings (auto-detected but can override)
FORCE_CPU_PROCESSING = False  # Set True to disable GPU
```

Then import and use in main.py:
```python
try:
    from config import *
    print("Using custom configuration")
except ImportError:
    print("Using default configuration")
```
