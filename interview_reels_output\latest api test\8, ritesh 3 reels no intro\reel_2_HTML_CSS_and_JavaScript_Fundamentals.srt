1
00:00:00,000 --> 00:00:06,879
In this time, you will, and I will repeat again, when do you and span in HTML?

2
00:00:06,879 --> 00:00:11,480
So, do you, do you is the mostly inline element?

3
00:00:11,480 --> 00:00:18,760
So, it will take whole like it will, it will take as much as space it can take in the horizontal

4
00:00:18,760 --> 00:00:19,760
direction.

5
00:00:19,760 --> 00:00:26,560
And span is a inline block element, which will only take the space HTML are mostly used

6
00:00:26,560 --> 00:00:27,560
for SEOs.

7
00:00:27,560 --> 00:00:34,920
So, the tags, the tag names specifically, specifically tell us about the art or the section

8
00:00:34,920 --> 00:00:40,480
of that particular, particular, what we say, particular element, let's say, let's say

9
00:00:40,480 --> 00:00:45,439
you are using a article, article tags, because you want to highlight the, highlight some,

10
00:00:45,439 --> 00:00:52,880
write some article or write some topic there, or if you are using the image tag, that

11
00:00:52,880 --> 00:00:53,880
is for the image.

12
00:00:53,880 --> 00:01:02,240
So, mostly semantic tags are used for class and ID in CSS is ID, ID we use for specifically

13
00:01:02,240 --> 00:01:11,280
a particular element to, to apply some CSS to that particular element, and ID can be used

14
00:01:11,280 --> 00:01:16,439
only on one element, whereas class, we can use for multiple elements, so that if we want

15
00:01:16,439 --> 00:01:24,159
to apply C to sign in JavaScript is a, when we compare double equal to sign, compare

16
00:01:24,159 --> 00:01:32,799
some, I am sorry, when we compare some elements with double equal to sign, it gives us, it

17
00:01:32,799 --> 00:01:37,120
gives us this one, let's again, let me try them.

18
00:01:37,120 --> 00:01:42,560
So, when we compare double equal to sign with some variables, it gives, it only does the

19
00:01:42,560 --> 00:01:49,840
type core, and it only compares the, it only compares the data of that, data of that

20
00:01:49,840 --> 00:01:55,960
particular, data of that particular variables, but the, but the, equal equal to sign, what

21
00:01:55,960 --> 00:02:02,680
is the, equal equal to sign, text for the, text for both, like, it takes for the type

22
00:02:02,680 --> 00:02:07,320
also, and it takes for the data also, if it is correct or not, so let's say, for example,

23
00:02:07,320 --> 00:02:14,319
if we use double equal to sign with, with a string, string for you, and with a,

