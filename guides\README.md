# Enhanced Interview Reel Creator v5.0

An advanced AI-powered tool that automatically creates engaging video reels from VAPI interview recordings with cutting-edge features including user-only segments, enhanced UI, black outro transitions, and Real-ESRGAN video enhancement.

## 🆕 Latest Features (v5.0)

### 1. User-Only Conversation Segments
- **Removes bot/interviewer parts** from video while preserving all topic moments
- **Smart timeline adjustment** for topic overlays to match new user-only timeline
- **Maintains conversation flow** by concatenating only candidate speaking segments
- **Preserves topic moment timing** with intelligent timestamp mapping

### 2. Enhanced UI Components (White/Black Theme)
- **Modern topic cards** with shadows, animations, and scaling effects
- **Professional typography** with Montserrat-Bold font support
- **Dynamic text sizing** based on content length and video dimensions
- **Smooth fade-in animations** and scaling effects for topic cards
- **Improved candidate name overlay** positioning and styling

### 3. Black Outro with Logo Fade
- **Smooth transition** to black screen at the end of each reel
- **Professional logo fade-in** effect (3-second outro duration)
- **Configurable outro duration** and logo sizing
- **Fallback to simple black screen** if logo not available

### 4. Real-ESRGAN Video Enhancement
- **Fully integrated Real-ESRGAN** for video super-resolution
- **Automatic model downloading** and GPU acceleration
- **Configurable enhancement levels** (low/medium/high)
- **Seamless fallback** if enhancement unavailable

## 🆕 Previous Features (v4.0)

### 1. Enhanced Silence Removal
- **Configurable parameters** for fine-tuning silence detection
- **Improved padding** around non-silent segments
- **Better performance** with seek_step optimization
- **Minimum clip duration** validation to prevent micro-clips

### 2. Real-ESRGAN Video Enhancement
- **Configurable enhancement levels**: low, medium, high
- **GPU acceleration** support with CUDA
- **Automatic model selection** based on enhancement level
- **Fallback to CPU** if GPU unavailable

### 3. Introduction Reel Generation
- **Separate LLM call** specifically for identifying candidate introductions
- **Automatic detection** of self-introduction segments
- **Same workflow** as regular reels with topic overlays
- **Dedicated transcript generation** for introduction content

## 📁 Project Structure

```
reels_creator/
├── main.py                     # Main script with all functionality
├── api_response.json          # VAPI interview data
├── assets/                    # Static assets (logos, fonts, music)
├── interview_reels_output/    # Generated reels and logs
│   ├── llm_logs/             # LLM call logs (topic and introduction)
│   ├── introduction_reel_*.mp4  # Introduction reels
│   ├── reel_*.mp4            # Topic-based reels
│   └── *_transcript.txt      # Reel transcripts
├── testing/                   # Testing files and resources
└── README.md                 # This file
```

## ⚙️ Configuration Options

### Silence Removal Settings
```python
SILENCE_THRESHOLD_DBFS = -45        # dB threshold for silence detection
MIN_SILENCE_DURATION_MS = 400       # Minimum silence duration to remove
NON_SILENT_PADDING_MS = 150         # Padding around non-silent segments
```

### Real-ESRGAN Enhancement Settings
```python
REALESRGAN_ENABLE = True            # Enable/disable video enhancement
REALESRGAN_ENHANCEMENT_LEVEL = 'medium'  # Options: 'low', 'medium', 'high'
```

### Enhancement Level Details:
- **Low**: 2x upscaling, smaller tile size (faster processing)
- **Medium**: 2x upscaling, standard tile size (balanced)
- **High**: 4x upscaling, larger tile size (best quality, slower)

## 🚀 Usage

1. **Prepare your environment:**
   ```bash
   conda activate reels_env
   pip install opencv-python moviepy mediapipe pydub google-generativeai
   # Optional for video enhancement:
   pip install realesrgan
   ```

2. **Configure your API key:**
   - Edit `main.py` and set your Google AI API key in `API_KEY_STRING`

3. **Place your VAPI response:**
   - Ensure `api_response.json` contains your interview data with `videoRecordingUrl`

4. **Run the script:**
   ```bash
   python main.py
   ```

5. **Enter candidate name when prompted**

## 📊 Output Files

The script generates:
- **Introduction reel**: `introduction_reel_*.mp4`
- **Topic reels**: `reel_1_*.mp4`, `reel_2_*.mp4`, etc.
- **Transcripts**: `*_transcript.txt` for each reel
- **LLM logs**: Detailed logs of AI analysis in `llm_logs/`

## 🔧 Technical Features

### Enhanced Silence Removal
- Uses pydub's `detect_nonsilent` with optimized parameters
- Configurable silence threshold and minimum duration
- Smart padding to preserve natural speech flow
- Minimum clip duration validation

### Real-ESRGAN Integration
- Automatic model downloading and caching
- GPU acceleration with CUDA support
- Configurable tile sizes for memory optimization
- Fallback mechanisms for stability

### Introduction Detection
- Dedicated LLM prompt for identifying introductions
- Validates segment length and content quality
- Generates topic moments for introduction overlays
- Separate logging for introduction analysis

## 📝 Logging

All LLM interactions are logged with timestamps:
- `topic_llm_call_*.txt`: Regular reel topic analysis
- `introduction_llm_call_*.txt`: Introduction segment analysis

## 🎯 Best Practices

1. **For optimal silence removal**: Adjust `SILENCE_THRESHOLD_DBFS` based on your audio quality
2. **For video enhancement**: Start with 'medium' level, adjust based on processing time vs quality needs
3. **For introduction reels**: Ensure interviews have clear self-introduction segments
4. **For performance**: Use GPU acceleration when available for Real-ESRGAN

## 🐛 Troubleshooting

- **Missing dependencies**: Install required packages as shown in usage section
- **GPU issues**: Set `REALESRGAN_ENABLE = False` to disable enhancement
- **No introduction found**: Check if candidate has clear self-introduction in transcript
- **Silence removal issues**: Adjust threshold values for your audio quality

## 📈 Performance Notes

- **Silence removal**: Reduces video duration by 10-30% typically
- **Video enhancement**: Adds 2-5x processing time depending on level
- **Introduction detection**: Adds ~10 seconds for LLM analysis
- **GPU acceleration**: 3-10x faster than CPU for video enhancement
