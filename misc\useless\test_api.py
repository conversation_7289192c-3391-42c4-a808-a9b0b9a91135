# -*- coding: utf-8 -*-
"""
Enhanced Interview <PERSON><PERSON> Creator v3.6 - Final Working Version
"""

import os
import sys
import json
import requests
import math
import warnings
import traceback
import time
import numpy as np
import cv2
from PIL import Image, ImageFont, ImageDraw, ImageFilter

# --- Dependency Imports with Error Handling ---
try:
    from moviepy.editor import *
    import moviepy.video.fx.all as vfx
    MOVIEPY_AVAILABLE = True
except ImportError:
    print("FATAL: moviepy is not installed. Please run 'pip install moviepy==1.0.3'.")
    sys.exit()

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except (ImportError, ValueError):
    print("Warning: mediapipe not installed. Run 'pip install mediapipe'.")
    MEDIAPIPE_AVAILABLE = False

try:
    from pydub import AudioSegment
    from pydub.silence import detect_nonsilent
    PYDUB_AVAILABLE = True
except ImportError:
    print("Warning: pydub is not installed. Run 'pip install pydub'.")
    PYDUB_AVAILABLE = False

try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False
    
# --- API KEY CONFIGURATION ---
API_KEY_STRING = "AIzaSyCxhaWlqcDsRgYMStXgRWXkDjl-fq386Wo" # <<< PASTE YOUR KEY HERE

if not GENAI_AVAILABLE or not API_KEY_STRING or API_KEY_STRING == "YOUR_API_KEY_HERE":
    print("🔴 ERROR: Script cannot run. Ensure 'google-generativeai' is installed and your API Key is set.")
    sys.exit()

# Configure the SDK once
genai.configure(api_key=API_KEY_STRING)

# --- Global Configuration ---
LLM_MODEL_NAME = "gemini-1.5-pro-latest"
OUTPUT_DIR = "interview_reels_output"
ASSETS_DIR = "assets"
VIDEO_FILENAME = "source_interview.mp4"
TEMP_AUDIO_FILENAME = "temp_segment_audio.wav"
LOGO_FILENAME = os.path.join(ASSETS_DIR, "logo.png")
FONT_FILENAME = os.path.join(ASSETS_DIR, "Roboto-Bold.ttf")

TEXT_COLOR = '#FFFFFF'
TEXT_FONTSIZE = 32
CANDIDATE_NAME_FONTSIZE = 24
TEXT_DURATION = 4
LOGO_FADE_DURATION = 2.0
CROP_RATIO = 9/16
FACE_SMOOTHING = 0.05
SILENCE_THRESHOLD_DBFS = -45
MIN_SILENCE_DURATION_MS = 400

warnings.filterwarnings("ignore", category=UserWarning)

# --- Core Functions ---

def load_api_response(filepath="api_response.json"):
    if not os.path.exists(filepath):
        print(f"Error: API response file not found at '{filepath}'")
        return None
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading file '{filepath}': {e}")
        return None

def download_file(url, filename):
    if os.path.exists(filename):
        print(f"File '{filename}' already exists. Skipping download.")
        return True
    try:
        print(f"Downloading from {url} to {filename}...")
        response = requests.get(url, stream=True, timeout=60)
        response.raise_for_status()
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print("Download complete.")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Error downloading file: {e}")
        return False

def get_message_end_time(message):
    start_sec = message.get('secondsFromStart')
    if start_sec is None: return None
    duration_ms = message.get('duration', 0)
    if duration_ms > 0:
        return start_sec + (duration_ms / 1000.0)
    if 'endTime' in message and 'time' in message:
        if isinstance(message['endTime'], (int, float)) and isinstance(message['time'], (int, float)):
            calculated_duration = message['endTime'] - message['time']
            if calculated_duration > 0:
                return start_sec + (calculated_duration / 1000.0)
    words_per_second = 2.5
    message_text = message.get('message', '')
    if message_text:
        word_count = len(message_text.split())
        estimated_duration = max(0.5, word_count / words_per_second)
        return start_sec + estimated_duration
    return start_sec + 0.5

def get_candidate_name():
    try:
        name = input("Enter candidate's name (or press Enter for 'Candidate'): ").strip()
        return name if name else "Candidate"
    except Exception:
        return "Candidate"

def crop_to_face_mediapipe(clip, crop_ratio=CROP_RATIO):
    if not MEDIAPIPE_AVAILABLE:
        print("MediaPipe not available, using simple center crop.")
        w, h = clip.size
        target_h = h
        target_w = int(target_h * crop_ratio)
        if target_w > w:
            target_w = w
            target_h = int(w / crop_ratio)
        return clip.fx(vfx.crop, width=target_w, height=target_h, x_center=w/2, y_center=h/2)

    mp_face_detection = mp.solutions.face_detection
    face_detection = mp_face_detection.FaceDetection(model_selection=1, min_detection_confidence=0.5)

    clip_w, clip_h = clip.w, clip.h
    target_w = int(clip_h * crop_ratio)
    target_h = clip_h
    if target_w > clip_w:
        target_w = clip_w
        target_h = int(clip_w / crop_ratio)
    smoothed_x = (clip_w - target_w) / 2
    
    def track_face_frame(get_frame, t):
        nonlocal smoothed_x
        frame = get_frame(t)
        h, w, _ = frame.shape
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = face_detection.process(rgb_frame)
        target_x = (w - target_w) / 2
        if results.detections:
            best_detection = max(results.detections, key=lambda d: d.score[0])
            bbox = best_detection.location_data.relative_bounding_box
            face_center_x = (bbox.xmin + bbox.width / 2) * w
            target_x = face_center_x - (target_w / 2)
        smoothing_factor = FACE_SMOOTHING
        smoothed_x = (smoothed_x * (1 - smoothing_factor)) + (target_x * smoothing_factor)
        final_x = int(max(0, min(smoothed_x, w - target_w)))
        final_y = int((h - target_h) / 2)
        return frame[final_y : final_y + target_h, final_x : final_x + target_w]

    print("Applying MediaPipe face tracking and cropping...")
    return clip.fl(track_face_frame)

def create_modern_topic_card(text, video_width, video_height, duration, start_time):
    try:
        base_font_size = max(24, int(video_width / 20))
        font_path = FONT_FILENAME if os.path.exists(FONT_FILENAME) else 'Arial.ttf'
        try:
            font = ImageFont.truetype(font_path, base_font_size)
        except IOError:
            font = ImageFont.load_default()
        bbox = font.getbbox(text.upper())
        text_w, text_h = bbox[2] - bbox[0], bbox[3] - bbox[1]
        padding = 20
        img_w, img_h = text_w + padding*2, text_h + padding*2
        img = Image.new('RGBA', (img_w, img_h), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        draw.rounded_rectangle((0, 0, img_w, img_h), radius=15, fill=(0, 0, 0, 150))
        draw.text((padding, padding), text.upper(), font=font, fill=TEXT_COLOR)
        text_clip = ImageClip(np.array(img)) \
            .set_duration(duration) \
            .set_start(start_time) \
            .set_position(('center', 0.1), relative=True)
        return text_clip.fx(vfx.fadein, 0.5).fx(vfx.fadeout, 0.5)
    except Exception as e:
        print(f"Failed to create modern topic card, using fallback. Error: {e}")
        return TextClip(
            text.upper(), fontsize=TEXT_FONTSIZE, color='white',
            bg_color='rgba(0,0,0,0.5)'
        ).set_duration(duration).set_start(start_time).set_position(('center', 'top'))

def create_candidate_name_overlay(candidate_name, video_width, video_height, duration):
    try:
        margin = int(video_width * 0.04)
        name_clip = TextClip(
            candidate_name, fontsize=CANDIDATE_NAME_FONTSIZE, color='#FFFFFF',
            font=FONT_FILENAME if os.path.exists(FONT_FILENAME) else 'Arial.ttf'
        ).set_duration(duration).set_position((margin, video_height - 50))
        return name_clip
    except Exception as e:
        print(f"Error creating candidate name overlay: {e}")
        return None

def create_end_logo_fade(video_width, video_height):
    if not os.path.exists(LOGO_FILENAME): return None
    try:
        logo_size = int(video_height * 0.15)
        logo_clip = ImageClip(LOGO_FILENAME) \
            .set_duration(LOGO_FADE_DURATION) \
            .resize(height=logo_size) \
            .set_position('center')
        bg_clip = ColorClip(size=(video_width, video_height), color=(0, 0, 0)) \
            .set_duration(LOGO_FADE_DURATION).set_opacity(0)
        return CompositeVideoClip([bg_clip.fx(vfx.fadein, LOGO_FADE_DURATION), logo_clip.fx(vfx.fadein, 0.5)])
    except Exception as e:
        print(f"Error creating end logo fade: {e}")
        return None

def remove_silence_from_clip(video_clip):
    if not PYDUB_AVAILABLE or video_clip.audio is None:
        return video_clip
    
    if not hasattr(video_clip.audio, 'fps') or video_clip.audio.fps is None:
        print("Audio clip is missing FPS (sample rate). Setting to default 44100.")
        video_clip.audio.fps = 44100

    temp_audio_path = os.path.join(OUTPUT_DIR, TEMP_AUDIO_FILENAME)
    try:
        print("Analyzing audio to remove silent parts...")
        video_clip.audio.write_audiofile(temp_audio_path, codec='pcm_s16le', logger=None)
        audio_segment = AudioSegment.from_file(temp_audio_path)
        non_silent_ranges = detect_nonsilent(
            audio_segment, min_silence_len=MIN_SILENCE_DURATION_MS, silence_thresh=SILENCE_THRESHOLD_DBFS
        )
        if not non_silent_ranges:
            print("No significant silences found.")
            return video_clip

        subclips = []
        padding_sec = 0.15
        for start_ms, end_ms in non_silent_ranges:
            start_sec = max(0, start_ms / 1000.0 - padding_sec)
            end_sec = min(video_clip.duration, end_ms / 1000.0 + padding_sec)
            if end_sec > start_sec:
                subclips.append(video_clip.subclip(start_sec, end_sec))

        if subclips:
            print(f"Silence removal complete. Original duration: {video_clip.duration:.2f}s, New duration: {sum(c.duration for c in subclips):.2f}s")
            return concatenate_videoclips(subclips, method="compose")
        else:
            return video_clip
    except Exception as e:
        print(f"Could not perform silence removal. Error: {e}. Proceeding with original clip.")
        return video_clip
    finally:
        if os.path.exists(temp_audio_path):
            try: os.remove(temp_audio_path)
            except OSError: pass

def get_topic_segments_from_llm(messages):
    conversation_text = "\n".join(
        f"[{msg.get('role', 'unknown')} at {msg.get('secondsFromStart'):.2f}s]: {msg.get('message', '')}"
        for msg in messages if msg.get('role') in ('user', 'bot') and msg.get('message')
    )
    prompt = f"""
    Analyze this interview transcript to create 1-3 short video reels. For each reel, pinpoint 2-4 specific "topic moments".
    Transcript:
    ---
    {conversation_text}
    ---
    Instructions:
    1. Identify 1-3 distinct `reel` segments.
    2. For each reel, give a short, catchy `reel_title`.
    3. Determine the `start_index` and `end_index` from the message list for the reel.
    4. For each reel, identify 2-4 `topic_moments`, each with a `timestamp` and a short `topic_text`.
    Return ONLY a valid JSON array of objects.
    """
    try:
        print("Contacting Google AI to analyze interview topics...")
        model = genai.GenerativeModel(LLM_MODEL_NAME)
        response = model.generate_content(prompt, generation_config={"temperature": 0.2})
        json_text = response.text.strip().replace("```json", "").replace("```", "")
        return json.loads(json_text)
    except Exception as e:
        print(f"Error during AI topic analysis: {e}")
        return None

def create_enhanced_interview_reels():
    print("--- Starting Enhanced Interview Reel Creator ---")
    data = load_api_response()
    if not data: return

    artifact = data.get('artifact', data)
    messages = artifact.get('messages')
    video_url = artifact.get('videoRecordingUrl')
    delay_seconds = artifact.get('videoRecordingStartDelaySeconds', 0)

    if not video_url or not messages:
        print("Critical data (videoRecordingUrl or messages) not found in JSON.")
        return

    candidate_name = get_candidate_name()
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    os.makedirs(ASSETS_DIR, exist_ok=True)

    video_filepath = os.path.join(OUTPUT_DIR, VIDEO_FILENAME)
    if not download_file(video_url, video_filepath): return

    reel_segments = get_topic_segments_from_llm(messages)
    if not reel_segments:
        print("Could not generate reel segments from LLM. Exiting.")
        return
    print(f"AI identified {len(reel_segments)} potential reel(s).")

    main_clip = VideoFileClip(video_filepath)

    for reel_count, reel_info in enumerate(reel_segments, 1):
        try:
            start_idx = reel_info['start_index']
            end_idx = reel_info['end_index']
            reel_title = reel_info['reel_title']
            topic_moments = reel_info.get('topic_moments', [])
            print(f"\n--- Processing Reel #{reel_count}: '{reel_title}' ---")
            
            reel_start_time_sec = messages[start_idx].get('secondsFromStart')
            if reel_start_time_sec is None: continue

            video_start = max(0, reel_start_time_sec - delay_seconds)
            video_end = min(main_clip.duration, get_message_end_time(messages[end_idx]) - delay_seconds)

            if video_end <= video_start: continue
            
            reel_clip = main_clip.subclip(video_start, video_end)
            face_tracked_video = crop_to_face_mediapipe(reel_clip)
            
            w, h = face_tracked_video.size
            duration = face_tracked_video.duration
            overlays = [face_tracked_video]
            
            print("Adding overlays...")
            for topic in topic_moments:
                ts = topic.get('timestamp')
                txt = topic.get('topic_text')
                relative_ts = ts - reel_start_time_sec
                if txt and ts is not None and 0 <= relative_ts < duration:
                    card = create_modern_topic_card(txt, w, h, TEXT_DURATION, relative_ts)
                    if card: overlays.append(card)
            
            if (name_overlay := create_candidate_name_overlay(candidate_name, w, h, duration)):
                overlays.append(name_overlay)
            
            final_reel = CompositeVideoClip(overlays, size=(w,h))
            final_reel_paced = remove_silence_from_clip(final_reel)
            
            # if (end_card := create_end_logo_fade(w, h)):
            #     end_card = end_card.set_start(final_reel_paced.duration)
            #     final_reel_paced = concatenate_videoclips([final_reel_paced, end_card.set_duration(LOGO_FADE_DURATION)], method="compose")

            clean_title = "".join(c for c in reel_title if c.isalnum() or c in (' ', '_')).strip().replace(' ', '_')
            output_path = os.path.join(OUTPUT_DIR, f"reel_{reel_count}_{clean_title}.mp4")
            
            print(f"Writing final video to '{output_path}'...")
            # --- THE FINAL FIX IS HERE ---
            final_reel_paced.write_videofile(output_path, codec='libx264', audio_codec='aac', threads=4, logger='bar')

        except Exception as e:
            print(f"!!! An unexpected error occurred while processing reel '{reel_title}' !!!")
            print(f"Error: {e}")
            traceback.print_exc()
            continue

    main_clip.close()
    print("\n--- All reels processed. ---")

if __name__ == "__main__":
    try:
        create_enhanced_interview_reels()
    except Exception as e:
        traceback.print_exc()