1
00:00:00,000 --> 00:00:07,320
So one of the biggest interesting problem that we faced was by sign and detection in order

2
00:00:07,320 --> 00:00:13,480
to ensure that a single doctor is using a single license with me, yeah, yeah.

3
00:00:13,480 --> 00:00:19,920
So the problem that I view facing is a multiple doctors are using a single license and we

4
00:00:19,920 --> 00:00:20,920
are losing that revenue.

5
00:00:20,920 --> 00:00:28,519
So we created a voice environment system to create MFCCs for those doctors to store them

6
00:00:28,519 --> 00:00:29,519
in the question.

7
00:00:29,519 --> 00:00:35,280
So initially we had created a gold standard voice biometric of the doctor and whenever

8
00:00:35,280 --> 00:00:39,519
the subsequent data would come, subsequent audio files would come, we could create a voice

9
00:00:39,519 --> 00:00:46,640
signature and then use it co-sign separately, we would find a match or so we used various

10
00:00:46,640 --> 00:00:47,640
methods.

11
00:00:47,640 --> 00:00:53,679
So first of all was a human evaluation, human was put in the loop, so whenever we find

12
00:00:53,679 --> 00:01:03,000
that co-sign has dropped to more than 70%, we trigger that and then a human is in the loop

13
00:01:03,000 --> 00:01:04,000
that is done.

14
00:01:04,000 --> 00:01:13,640
But we also did a initial project requirement, it was okay to trigger false negatives which

15
00:01:13,640 --> 00:01:20,480
is basically then a human would be able to say that okay, this is not a fake alarm,

16
00:01:20,480 --> 00:01:23,040
but we do it not so.

