import os
import subprocess
from faster_whisper import WhisperModel
FFMPEG_PATH = r"C:\ffmpeg\ffmpeg.exe"


def format_timestamp(seconds: float) -> str:
    millis = int((seconds - int(seconds)) * 1000)
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"

def generate_srt_from_mp4(mp4_path: str, srt_path: str, model_size='base'):
    """
    Generate an .srt subtitle file from the audio of a .mp4 video using faster_whisper on GPU.

    Args:
        mp4_path (str): Path to input video file (.mp4).
        srt_path (str): Path to output subtitle file (.srt).
        model_size (str): Whisper model size ('tiny', 'base', 'small', etc.).
    """
    temp_audio_path = "temp_audio.wav"
    try:
        # Extract audio as 16kHz mono wav using ffmpeg
        subprocess.run([
            FFMPEG_PATH, '-y', '-i', mp4_path,
            '-ar', '16000', '-ac', '1', temp_audio_path
        ], check=True)
        print("ffmpeg extraction complete!")
        print("Loading WhisperModel...")

        # Use GPU with 'cuda' device
        model = WhisperModel(model_size, device='cpu')  # float16 for GPU efficiency
        print("Transcribing audio...")

        segments, info = model.transcribe(temp_audio_path)
        print("Transcription complete, writing srt...")

        with open(srt_path, 'w', encoding='utf-8') as srt_file:
            for i, segment in enumerate(segments, start=1):
                start_time = format_timestamp(segment.start)
                end_time = format_timestamp(segment.end)
                text = segment.text.strip()
                srt_file.write(f"{i}\n{start_time} --> {end_time}\n{text}\n\n")
        print(f"Subtitle file saved: {srt_path}")

    except Exception as e:
        print(f"Failed to generate SRT subtitles: {e}")
    finally:
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
