1
00:00:00,000 --> 00:00:04,200
I'm majorly focused on audio and text part of the things.

2
00:00:04,200 --> 00:00:07,040
My experience range is from

3
00:00:07,040 --> 00:00:09,839
Dr<PERSON> <PERSON><PERSON><PERSON>, taking Dr<PERSON> <PERSON><PERSON><PERSON> transcripts,

4
00:00:09,839 --> 00:00:13,400
we're using audio, converting them to text,

5
00:00:13,400 --> 00:00:15,960
and then using, yeah, so I have used

6
00:00:17,080 --> 00:00:21,080
various sources for audio transcription.

7
00:00:21,080 --> 00:00:23,760
Maybe it can be Amazon medical transcribe

8
00:00:23,760 --> 00:00:26,879
or Azure real-time SDK, as well as the gram

9
00:00:26,879 --> 00:00:30,279
multiple having used and created custom vocals.

