1
00:00:00,000 --> 00:00:06,560
double equals to check for only value equality, wireless triple equals to check for both value and

2
00:00:07,280 --> 00:00:13,759
type equality, data type equality. So for example, for only both data type and the value is equal,

3
00:00:14,720 --> 00:00:21,280
it's triple equal to will return true. For example, for example, let's say I want to compare a string

4
00:00:21,280 --> 00:00:28,400
5 with number 5. So I have to use triple equals to this. If I use double equals to then it might

5
00:00:28,400 --> 00:00:34,719
because that I get true, but knowing that it's data type is not same, that statement is not

6
00:00:34,719 --> 00:00:44,320
correct. So I have to use triple equal to in this case.

